@charset "UTF-8";
/*-----------------------------------------------------------------------------------

  
--------------------------
    CSS INDEX
--------------------------

    01. Common css 
    02. Header css
    03. Hero css
    04. About css
    05. Elements css
    06. Features css
    07. Services css
    08. Activity css
    09. We css
    10. Gallery css
    11. Booking css
    12. Places css
    13. Testimonial css
    14. Shop css
    15. Blog css
    16. Contact css
    17. Footer css
    18. Sliders css
    19. Wrapper css

-----------------------------------------------------------------------------------*/
@import url('https://fonts.googleapis.com/css2?family=Leckerli+One&family=Merienda:wght@300;400;500;600;700;800;900&family=Poppins:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Wix+Madefor+Display:wght@400;500;600;700;800&display=swap');


.contact-info-item .info p, .sidebar-widget-area .booking-info-widget ul.info-list li span span, .sidebar-widget-area .booking-form-widget .sidebar-booking-form .booking-total .total, .sidebar-widget-area .booking-form-widget .sidebar-booking-form .booking-extra .extra, .sidebar-widget-area .booking-form-widget .sidebar-booking-form .booking-item label, .sidebar-widget-area .tag-cloud-widget a, .blog-post .entry-content .block-quote span, .single-blog-post-four .entry-content .main-btn.filled-btn:hover i, .gw-testimonial-item-two .testimonial-inner-content > p, .gw-testimonial-item .testimonial-inner-content > p, .tour-area-nav .share-nav a, .related-tour-place .place-arrows .slick-arrow:hover, .booking-form-two .booking-btn, .activity-nav-tab .nav-tabs li a, .single-service-item-four:hover .content h3.title, .single-service-item-three .content .meta span.rate, .single-service-item .content .icon-btn, .check-list li, .single-skill-circle .inner-circle .number, .hero-wrapper-two .hero-content .hero-button .main-btn.primary-btn i, .nav-right-item .lang-dropdown .nice-select ul.list, .header-navigation .main-menu ul > li .sub-menu li a, .btn-link, .main-btn.filled-btn:hover i, .main-btn.filled-btn, .main-btn i, h6, h5, h4, h3, h2, h1 {
  color: #1C231F;
}

.footer-copyright .footer-nav ul li a:hover, .service-nav-widget .footer-content .footer-widget-nav li a:hover, .footer-top .social-box ul.social-link li a, .black-bg .single-info-item .info p:hover a, .contact-info-item .info p:hover a, .sidebar-widget-area .recent-place-widget .recent-place-list .place-thumbnail-content .place-content span.price, .sidebar-widget-area .recent-place-widget .recent-place-list .place-thumbnail-content .place-content h5:hover, .sidebar-widget-area .booking-info-widget ul.info-list li span i, .sidebar-widget-area .booking-form-widget .sidebar-booking-form .booking-extra .extra i, .sidebar-widget-area .booking-form-widget .sidebar-booking-form .bk-item i, .sidebar-widget-area .sidebar-banner-widget .banner-widget-content .banner-img .hover-content p i, .sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content .post-title-date h5:hover, .sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content .post-title-date span.posted-on:hover, .sidebar-widget-area .category-widget ul.category-nav li a:hover, .comments-area .comment .comment-wrap .comment-author-content span.author-name span.time i, .post-navigation-item .post-nav-item .content h6:hover, .single-blog-post-four .entry-content h3.title:hover, .single-blog-post-three .entry-content h3.title:hover, .single-blog-post-two .entry-content h3.title:hover, .post-meta span:hover a, .description-tabs .nav-link:hover, .description-tabs .nav-link.active, .product-details-wrapper .product-info span.price, .single-product-item .content .info h4.title:hover, .destination-details-wrapper .destination-info .meta span.location i, .days-area .nav-tabs .nav-link.active, .tour-title-wrapper .tour-widget-info .info-box .icon, .tour-title-wrapper .tour-title p i, .single-place-item-three:hover .place-content h4.title, .single-place-item .place-content .info .meta span a:hover, .single-place-item .place-content .info .meta span i, .single-place-item .place-content .info p i, .single-place-item .place-content .info h4.title:hover, .booking-form-two .form_group label, .booking-form .nice-select:after, .booking-form label i, .activity-content-box .icon i, .single-service-item-two:hover .content .icon i, .check-list li i, .single-info-item-two .info h5:hover a, .single-info-item-two .icon, .single-event-item .content h3.title:hover, .single-event-item .content .meta span i, .single-activity-item .content .main-btn:hover i, .single-counter-item-two .icon, .single-counter-item .icon, .counter-item h2.number, .single-team-item .member-info ul.social-link li a:hover, .fancy-icon-box-four .icon i, .fancy-icon-box-three .icon, .fancy-icon-box-two .icon, .single-features-list .icon-inner .icon, .hero-wrapper-three .hero-arrows .slick-arrow:hover, .navigation-white.header-navigation .main-menu ul > li .search-btn:hover, .navigation-white.header-navigation .main-menu ul > li:hover > a, .nav-right-item .search-btn:hover, .header-navigation.breakpoint-on .nav-search .search-btn, .header-navigation .main-menu ul > li:hover > a, .header-navigation .main-menu ul > li .sub-menu li:hover > a, .header-navigation .main-menu ul > li .sub-menu li a:hover, .header-navigation .main-menu ul > li.search-item .search-btn:hover, .btn-link:hover, .sub-title {
  color: #ff9d00;
}

.reviews-wrapper .reviews-inner-box .rating-value .rate-score, .black-bg .sub-title, .footer-newsletter-widget .footer-content form label, .comment-rating-ul li span i, .post-author-box .author-content ul.social-link li a:hover, .blog-post .entry-footer .social-share a:hover, .single-blog-post .entry-content h3.title:hover, .product-details-wrapper .product-info .product-meta li a:hover, .product-details-wrapper .product-info ul.ratings li a:hover, .destination-details-wrapper .destination-info .features-list li span i, .single-service-item-four .content .icon-btn, .single-service-item-three .content .meta span.rate i, .single-service-item-two .content .icon i, .ratings li i, .single-event-item .content p.location i, .single-activity-item .content .meta .rate i, .single-features-list .icon-inner .icon-check i, .about-two_content-box .card-list .card-item i {
  color: #ff9d00;
}

.single-place-item .place-content .info .meta span a i, .single-place-item .place-content .info .meta span a, .header-navigation .main-menu ul > li > a {
  color: #1D231F;
}

.reviews-wrapper .reviews-inner-box .rating-value span.reviews, .hero-slider-two .slick-arrow:hover, .hero-slider-one .slick-arrow:hover, .footer-top .social-box ul.social-link li a:hover, .black-bg .single-cta-item .content h3.title, .black-bg .single-info-item .info span.title, .black-bg .footer-top .social-box ul.social-link li a:hover, .black-bg .footer-widget h4.widget-title, .sidebar-widget-area .tag-cloud-widget a:hover, .sidebar-widget-area .sidebar-banner-widget .banner-widget-content .banner-img .hover-content p, .sidebar-widget-area .sidebar-banner-widget .banner-widget-content .banner-img .hover-content h4.title, .blog-post .entry-footer .tag-links a:hover, .cat-btn, .product-details-wrapper .product-info ul.social-link li a:hover, .single-product-item .img-holder .tag span, .tour-area-nav .share-nav a:hover, .single-place-item-three:hover .place-content .meta .icon-btn, .single-place-item-two .place-img .tour-count, .booking-form-two .booking-btn:hover, .single-gallery-item .gallery-img .icon-btn, .activity-content-box ul.check-list li, .activity-nav-tab .nav-tabs li a.active, .single-service-item-four .content h3.title, .single-service-item-four:hover .content .icon-btn, .single-service-item-two .content .btn-link, .single-service-item-two .content p, .single-service-item-two .content h3.title, .single-service-item:hover .content .icon-btn, .ribbon, .gowilds-pagination li a.active, .gowilds-pagination li a:hover, .single-info-item .icon, .single-cta-item .content .icon-btn, .single-activity-item .content .main-btn i, .single-counter-item:hover .icon, .fancy-icon-box-three:hover .text .btn-link, .fancy-icon-box-three:hover .text h5.title, .fancy-icon-box-two:hover .icon, .fancy-icon-box:hover .icon, .single-features-item-three:hover .content h6, .single-features-item-two .content h3.title, .single-features-item .img-holder .text .icon-btn, .hero-wrapper-three .hero-arrows .slick-arrow, .navigation-white.header-navigation .nav-right-item .lang-dropdown .nice-select, .navigation-white.header-navigation .main-menu ul > li .search-btn, .navigation-white.header-navigation .main-menu ul > li > a, .header-three .header-navigation .primary-menu.black-bg .nav-right-item .lang-dropdown .nice-select, .nav-right-item .search-btn, .header-navigation.breakpoint-on .nav-menu .main-menu ul li .dd-trigger, .back-to-top:hover, .back-to-top:focus, .main-btn.filled-btn.filled-white, .main-btn.filled-btn:hover, .main-btn.filled-btn i, .main-btn.secondary-btn, .main-btn.primary-btn, .text-white p, .text-white h1, .text-white h2, .text-white h3, .text-white h4, .text-white h5, .text-white h6 {
  color: #ffffff;
}

.reviews-wrapper .reviews-inner-box .rating-value span.reviews, .fun-wrapper, ul.slick-dots li:after, .footer-top .social-box ul.social-link li a:hover, .black-bg .footer-top .social-box ul.social-link li a:hover, .sidebar-widget-area .tag-cloud-widget a:hover, .blog-post .entry-footer .tag-links a:hover, .single-product-item .img-holder .tag span.off, .tour-area-nav .share-nav a:hover, .single-place-item-three:hover .place-content .meta .icon-btn, .booking-form-two .booking-btn:hover, .gowilds-pagination li a.active, .gowilds-pagination li a:hover, .single-info-item .icon, .single-cta-item .content .icon-btn, .single-counter-item:hover .icon, .fancy-icon-box-three:hover, .fancy-icon-box-two:hover .icon, .fancy-icon-box:hover .icon, .single-features-item-three:hover .content, .single-features-item .img-holder .text .icon-btn, .single-features-item .img-holder .text h4.title:before, .header-navigation.breakpoint-on .nav-menu .main-menu ul li .dd-trigger, .main-btn.filled-btn.filled-white:hover, .main-btn.filled-btn:hover, .main-btn.filled-btn i, .main-btn.secondary-btn:hover, .main-btn.primary-btn {
  background-color: #ff9d00;
}

.hero-slider-two .slick-arrow:hover, .hero-slider-one .slick-arrow:hover, .blog-post .entry-content .block-quote span:before, .single-blog-post-four .entry-content .main-btn.filled-btn:hover, .single-blog-post-four .entry-content .main-btn.filled-btn i, .cat-btn, .description-wrapper .content-box ul.check-style-one li i, .product-details-wrapper .product-info ul.social-link li a:hover, .single-product-item .img-holder .tag span.feat, .single-place-item-two .place-img .tour-count, .single-gallery-item .gallery-img .icon-btn, .activity-nav-tab .nav-tabs li a.active, .single-service-item-four:hover .content .icon-btn, .single-service-item:hover .content .icon-btn, .ribbon, .single-progress-bar .progress .progress-bar, .single-cta-item:nth-child(2) .content .icon-btn, .back-to-top:hover, .back-to-top:focus, .main-btn.secondary-btn, .main-btn.primary-btn:hover {
  background-color: #ff9d00;
}

.single-activity-item .content .main-btn i, .header-two .header-navigation.sticky, .header-one .header-navigation.sticky, .black-bg {
  background-color: #0f238d;
}

.skill-wrapper, .footer-newsletter-widget .footer-content form .form_control, .sidebar-widget-area .booking-form-widget .sidebar-booking-form .bk-item .nice-select, .sidebar-widget-area .booking-form-widget .sidebar-booking-form .bk-item input, .sidebar-widget-area .sidebar-widget, .single-blog-post-four .entry-content .main-btn.filled-btn:hover i, .gw-testimonial-item-two, .destination-details-wrapper .destination-info .features-list li span, .single-place-item-three .place-content .meta .icon-btn, .single-place-item-three:hover, .single-place-item .place-content .info, .booking-form .nice-select:focus, .booking-form .form_control:focus, .booking-form-wrapper, .activity-nav-tab, .single-service-item-four .content .icon-btn, .single-service-item-four:hover, .single-service-item-three .content .meta span.rate, .single-service-item-three:hover, .single-service-item .content .icon-btn, .single-service-item, .video-popup, .single-event-item, .single-activity-item .content .meta, .single-counter-item-two .icon, .single-counter-item, .single-team-item, .fancy-icon-box-three .icon, .fancy-icon-box-two, .fancy-icon-box .icon, .single-features-list .icon-inner .icon, .single-features-item-three .content, .single-features-item .img-holder .content, .about-two_content-box .card-list .card-item, .hero-search-form, .hero-wrapper-three .hero-arrows .slick-arrow:hover, .navigation-white.header-navigation .navbar-toggler span, .header-four .header-navigation.sticky, .header-navigation.breakpoint-on .nav-menu, .header-navigation .main-menu ul > li .sub-menu, .preloader, .main-btn.filled-btn:hover i, .main-btn i, .white-bg {
  background-color: #ffffff;
}

.contact-form .form_control, .contact-info-item, .sidebar-widget-area .booking-form-widget, .sidebar-widget-area .tag-cloud-widget, .sidebar-widget-area .search-widget, .comments-respond, .blog-post .entry-content .block-quote, .review-form-area, .days-area .nav-tabs, .single-place-item-three, .booking-form .nice-select, .booking-form .form_control, .activity-nav-tab .nav-tabs li a, .single-service-item-three, .single-activity-item .content, .fancy-icon-box-three, .fancy-icon-box-two .icon, .fancy-icon-box, .gray-bg {
  background-color: #ffffff;
}

h1 {
  font-size: 65px;
  font-weight: 700;
  line-height: 1.12;
}

h2 {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
}

h3 {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.3;
}

h4 {
  font-size: 22px;
  font-weight: 600;
  line-height: 1.37;
}

h5 {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.3;
  
}

h6 {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.2;
}

.hero-slider-two .slick-arrow, .hero-slider-one .slick-arrow, .footer-top .social-box ul.social-link li a, .product-details-wrapper .product-info ul.social-link li a, .single-place-item-three .place-content .meta .icon-btn, .single-gallery-item .gallery-img .icon-btn, .single-gallery-item .gallery-img .hover-overlay, .single-service-item-four .content .icon-btn, .single-service-item .content .icon-btn, .video-popup, .gowilds-pagination li a, .single-skill-circle .inner-circle, .single-info-item-two .icon, .single-info-item .icon, .single-cta-item .content .icon-btn, .single-counter-item-two .icon, .single-counter-item .icon, .fancy-icon-box-three .icon, .fancy-icon-box-two .icon, .fancy-icon-box .icon, .single-features-list .icon-inner .icon, .single-features-item .img-holder .text .icon-btn, .hero-wrapper-three .hero-arrows .slick-arrow, .header-navigation.breakpoint-on .nav-menu .main-menu ul li .dd-trigger, .preloader, .main-btn i {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

/*---========================
        01. Default css 
=======================----*/
/* Base CSS */
html {
  font-size: 100%;
}

* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

a {
  color: inherit;
  text-decoration: none;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
a:hover, a:focus {
  color: inherit;
  text-decoration: none;
}

a:focus,
input:focus,
textarea:focus,
button:focus {
  text-decoration: none;
  outline: none;
}

i,
span,
a {
  display: inline-block;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0px;
  font-family: 'Raleway', sans-serif;
  ;
}

@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  h2 {
    font-size: 48px;
  }
}
@media (max-width: 1199px) {
  h2 {
    font-size: 36px;
  }
}
@media (max-width: 767px) {
  h2 {
    font-size: 24px;
  }
}

ul, ol {
  margin: 0px;
  padding: 0px;
  list-style-type: none;
}

p {
  margin: 0px;
}

input, textarea {
  display: inherit;
}

label {
  margin-bottom: 0;
}

iframe {
  width: 100%;
  border: none;
  display: inherit;
}

img {
  max-width: 100%;
}

body {
  font-weight: normal;
  font-style: normal;
  font-weight: 300;
  color: #484848;
  font-family: "Prompt", sans-serif;
  font-size: 16px;
  line-height: 29.6px;
  overflow-x: hidden;
}
.raleway{
  font-family: 'Raleway'sans-serif ;
  font-size: medium;
}
/*====== Extra CSS ======*/
.container-fluid {
  padding-left: 70px;
  padding-right: 70px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media (max-width: 1199px) {
  .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.mbm-150 {
  margin-bottom: -130px;
}

@media only screen and (min-width: 1200px) {
  .plr-5p {
    padding-left: 5%;
    padding-right: 5%;
  }

  /* Margin Left */
  .ml-lg-40 {
    margin-left: 40px;
  }

  .ml-lg-45 {
    margin-left: 45px;
  }

  .ml-lg-55 {
    margin-left: 55px;
  }

  .ml-lg-60 {
    margin-left: 60px;
  }

  .ml-lg-70 {
    margin-left: 70px;
  }

  .ml-minus-lg-60 {
    margin-left: -60px;
  }

  /* Margin Right */
  .mr-lg-40 {
    margin-right: 40px;
  }

  .mr-lg-50 {
    margin-right: 50px;
  }

  .mr-lg-70 {
    margin-right: 70px;
  }

  .mr-lg-100 {
    margin-right: 100px;
  }

  /* Padding Left */
  .pl-lg-20 {
    padding-left: 20px;
  }

  .pl-lg-30 {
    padding-left: 30px;
  }

  .pl-lg-40 {
    padding-left: s0px;
  }

  .pl-lg-45 {
    padding-left: 45px;
  }

  .pl-lg-50 {
    padding-left: 50px;
  }

  .pl-lg-55 {
    padding-left: 55px;
  }

  .pl-lg-60 {
    padding-left: 60px;
  }

  .pl-lg-70 {
    padding-left: 70px;
  }

  .pl-lg-80 {
    padding-left: 80px;
  }

  .pl-lg-100 {
    padding-left: 100px;
  }

  /* Padding Right */
  .pr-lg-30 {
    padding-right: 30px;
  }

  .pr-lg-40 {
    padding-right: 40px;
  }

  .pr-lg-50 {
    padding-right: 50px;
  }

  .pr-lg-60 {
    padding-right: 60px;
  }

  .pr-lg-70 {
    padding-right: 70px;
  }
}
ul.social-link li {
  display: inline-block;
}

.bg_cover {
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}

.slick-slide {
  outline: 0;
}

.form_group {
  position: relative;
}

.form_control {
  width: 100%;
  border: none;
}

textarea.form_control {
  padding-top: 15px;
  display: inherit;
}

.p-r {
  position: relative;
}

.z-1 {
  z-index: 1;
}

.z--1 {
  z-index: -1;
}

.z-2 {
  z-index: 2;
}

.sub-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
  padding: 10px 20px;
  background: rgba(5, 2, 48, 0.1);
  border-radius: 7px;
}

.section-title span.sub-title {
  margin-bottom: 13px;
}

/*===== All Bg =====*/
.black-dark-bg {
  background-color: #272C28;
}
.novus-bg {
  background-color: #0f238d;
}
/*===== All Button Style =====*/
button {
  border: none;
}

.main-btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 3px 3px 3px 40px;
  font: 400 16px "Prompt", sans-serif;
  text-transform: capitalize;
  border-radius: 28px;
  line-height: 20px;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.main-btn i {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  margin-left: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 18px;
}
.main-btn.filled-btn {
  padding: 2px 3px 2px 40px;
  background-color: transparent;
  border: 1px solid rgba(0, 0, 0, 0.1);
}
.main-btn.filled-btn.filled-white {
  border-color: #f0f9ff;
}
.main-btn.filled-btn.filled-white:hover {
  border-color: transparent;
}

.btn-link {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  text-decoration: underline;
}
.btn-link i {
  margin-left: 7px;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.btn-link:hover i {
  margin-left: 10px;
}

/*====== Custom Animation =======*/
@-webkit-keyframes shake {
  0% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
  50% {
    -webkit-transform: translateX(7px);
            transform: translateX(7px);
  }
  100% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
}
@keyframes shake {
  0% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
  50% {
    -webkit-transform: translateX(7px);
            transform: translateX(7px);
  }
  100% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
}
.animate-float-x {
  -webkit-animation-name: float-x;
          animation-name: float-x;
  -webkit-animation-duration: 2s;
          animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
          animation-timing-function: linear;
}

.animate-float-y {
  -webkit-animation-name: float-y;
          animation-name: float-y;
  -webkit-animation-duration: 2s;
          animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
          animation-timing-function: linear;
}

@-webkit-keyframes float-y {
  0% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
  }
  50% {
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
  }
}
@keyframes float-y {
  0% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
  }
  50% {
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
  }
}
@-webkit-keyframes float-x {
  0% {
    -webkit-transform: translateX(-20px);
            transform: translateX(-20px);
  }
  50% {
    -webkit-transform: translateX(-10px);
            transform: translateX(-10px);
  }
  100% {
    -webkit-transform: translateX(-20px);
            transform: translateX(-20px);
  }
}
@keyframes float-x {
  0% {
    -webkit-transform: translateX(-20px);
            transform: translateX(-20px);
  }
  50% {
    -webkit-transform: translateX(-10px);
            transform: translateX(-10px);
  }
  100% {
    -webkit-transform: translateX(-20px);
            transform: translateX(-20px);
  }
}
@-webkit-keyframes wave {
  0%, 100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  50% {
    -webkit-transform: translateX(100px);
            transform: translateX(100px);
  }
}
@keyframes wave {
  0%, 100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  50% {
    -webkit-transform: translateX(100px);
            transform: translateX(100px);
  }
}
/*====== Search Modal ======*/
.search-modal {
  background-color: rgba(13, 18, 23, 0.95);
}
.search-modal .modal-content {
  padding: 15px 30px;
  background-color: transparent;
  border: none;
}
.search-modal .modal-content label {
  position: absolute;
  top: 15px;
  right: 0;
  color: #f0f9ff;
}
.search-modal .modal-content .form_control {
  padding: 15px 30px 15px 0;
  background-color: transparent;
  color: #f0f9ff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.7);
}
.search-modal .modal-content .form_control::-webkit-input-placeholder {
  color: #f0f9ff;
}
.search-modal .modal-content .form_control::-moz-placeholder {
  color: #f0f9ff;
}
.search-modal .modal-content .form_control:-ms-input-placeholder {
  color: #f0f9ff;
}
.search-modal .modal-content .form_control::-ms-input-placeholder {
  color: #f0f9ff;
}
.search-modal .modal-content .form_control::placeholder {
  color: #f0f9ff;
}
.search-modal .modal-content .form_control:focus {
  background-color: transparent;
}

.modal-open {
  overflow: auto !important;
  padding-right: 0 !important;
  overflow-x: hidden !important;
}
.modal-open .modal.show {
  padding-right: 0 !important;
  overflow-x: hidden !important;
  overflow-y: hidden !important;
}

/*===== Nice Select =====*/
.nice-select {
  padding: 0 10px;
  border: none;
  border-radius: 0px;
  font-size: 16px;
}
.nice-select:after {
  position: absolute;
  right: 0;
  content: "";
  font-family: "Font Awesome 5 Pro";
  font-weight: 400;
}
.nice-select ul.list {
  border-radius: 0;
}
.nice-select .option {
  padding: 0 10px;
}

/*====== Start Preloader css ======*/
.preloader {
  bottom: 0;
  height: 100vh;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  width: 100vw;
  z-index: 99999;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
}
.preloader.fade-out {
  opacity: 0;
  visibility: hidden;
}
.preloader .loader {
  position: relative;
  width: 50px;
  height: 50px;
}
.preloader .pre-box {
  width: 50px;
  height: 50px;
  background: #ff9d00;
  -webkit-animation: loaderAnimate 0.8s ease-in-out infinite;
          animation: loaderAnimate 0.8s ease-in-out infinite;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 4px;
  will-change: transform;
}
.preloader .pre-shadow {
  width: 50px;
  height: 5px;
  background: rgba(0, 0, 0, 0.15);
  position: absolute;
  top: 59px;
  left: 0;
  border-radius: 50%;
  -webkit-animation: loaderShadow 0.8s ease-in-out infinite;
          animation: loaderShadow 0.8s ease-in-out infinite;
  will-change: transform;
}

@-webkit-keyframes loaderAnimate {
  0%, 100% {
    -webkit-transform: translateY(0) rotate(0deg);
            transform: translateY(0) rotate(0deg);
  }
  50% {
    -webkit-transform: translateY(15px) rotate(180deg);
            transform: translateY(15px) rotate(180deg);
  }
}
@keyframes loaderAnimate {
  0%, 100% {
    -webkit-transform: translateY(0) rotate(0deg);
            transform: translateY(0) rotate(0deg);
  }
  50% {
    -webkit-transform: translateY(15px) rotate(180deg);
            transform: translateY(15px) rotate(180deg);
  }
}
@-webkit-keyframes loaderShadow {
  0%, 100% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
    opacity: 0.15;
  }
  50% {
    -webkit-transform: scale(1.3, 1);
            transform: scale(1.3, 1);
    opacity: 0.05;
  }
}
@keyframes loaderShadow {
  0%, 100% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
    opacity: 0.15;
  }
  50% {
    -webkit-transform: scale(1.3, 1);
            transform: scale(1.3, 1);
    opacity: 0.05;
  }
}
/*====== Start Back to top css ======*/

.back-to-top {
  background-color: #ff9d00;
  border-radius: 50%;
  bottom: 30px;
  color: #f0f9ff;
  cursor: pointer;
  display: none;
  font-size: 20px;
  width: 50px;
  height: 50px;
  line-height: 50px;
  position: fixed;
  right: 30px;
  text-align: center;
  text-decoration: none;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  z-index: 337;
}
/*====================
    End COMMON css 
======================*/
/*---==================
    02. Header css 
=================----*/
/* Transparent Header  */
.transparent-header {
  position: absolute;
  background-color: transparent;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
}
@media (max-width: 991px) {
  .transparent-header {
    top: 0;
  }
}

/* Header Navigation */
.header-navigation .nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  visibility: hidden;
  opacity: 0;
  z-index: 2;
}
.header-navigation .nav-overlay.active {
  visibility: visible;
  opacity: 1;
}
.header-navigation .primary-menu {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.header-navigation .main-menu ul > li {
  display: inline-block;
  position: relative;
  margin-left: 17px;
  margin-right: 17px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .header-navigation .main-menu ul > li {
    margin-left: 10px;
    margin-right: 10px;
  }
}
.header-navigation .main-menu ul > li.search-item .search-btn {
  cursor: pointer;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.header-navigation .main-menu ul > li > a {
  position: relative;
  display: block;
  font: 600 15px "Prompt", sans-serif;
  padding: 34px 0 37px;
  text-transform: capitalize;
  line-height: 1;
}
.header-navigation .main-menu ul > li > a span.dd-trigger {
  margin-left: 5px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .header-navigation .main-menu ul > li > a {
    font-size: 16px;
  }
}

.header-navigation .main-menu ul > li .sub-menu {
  position: absolute;
  left: 0;
  top: 120%;
  width: 250px;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
  z-index: 99;
  padding: 15px 0;
  height: auto;
  text-align: left;
  border-radius: 5px;
  -webkit-box-shadow: 0 30px 70px 0 rgba(137, 139, 142, 0.15);
          box-shadow: 0 30px 70px 0 rgba(137, 139, 142, 0.15);
}
.header-navigation .main-menu ul > li .sub-menu li {
  display: block;
  margin: 0 20px 10px;
  border-bottom: 1px solid #f0f9ff;
}
.header-navigation .main-menu ul > li .sub-menu li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0 5px 10px;
  position: relative;
  line-height: 1.5;
  margin: 0;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.header-navigation .main-menu ul > li .sub-menu li a span.dd-trigger {
  float: right;
}
.header-navigation .main-menu ul > li .sub-menu li:last-child {
  border-bottom: 0;
  margin-bottom: 0;
}
.header-navigation .main-menu ul > li .sub-menu li:last-child a {
  padding-bottom: 0;
}
.header-navigation .main-menu ul > li .sub-menu li .sub-menu {
  left: 100%;
  top: 50%;
}
.header-navigation .main-menu ul > li .sub-menu li:hover .sub-menu {
  top: 0%;
}
.header-navigation .main-menu ul > li:hover > .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 100%;
}
.header-navigation.breakpoint-on .nav-search .form_control {
  border: 1px solid #e1e1e1;
  padding: 15px 20px;
}
.header-navigation.breakpoint-on .nav-search .search-btn {
  position: absolute;
  top: 17px;
  right: 20px;
  background-color: transparent;
}
.header-navigation.breakpoint-on .nav-menu {
  text-align: left;
  position: fixed;
  top: 0;
  left: -290px;
  width: 290px;
  height: 100%;
  -webkit-transition-duration: 500ms;
          transition-duration: 500ms;
  padding: 40px 20px;
  -webkit-box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
          box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  display: block;
  overflow-x: hidden;
  overflow-y: scroll;
  z-index: 9999;
}
.header-navigation.breakpoint-on .nav-menu.menu-on {
  left: 0;
}
.header-navigation.breakpoint-on .nav-menu .main-menu ul li {
  display: block;
  margin: 0;
  border-bottom: 1px solid #f0f9ff;
}
.header-navigation.breakpoint-on .nav-menu .main-menu ul li.search-item {
  display: none;
}
.header-navigation.breakpoint-on .nav-menu .main-menu ul li a {
  display: block;
  padding: 15px 0;
  color: #000;
}
.header-navigation.breakpoint-on .nav-menu .main-menu ul li .sub-menu {
  width: 100%;
  position: relative;
  top: 0;
  left: 0;
  padding: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  background-color: transparent;
  visibility: visible;
  opacity: 1;
  display: none;
  -webkit-transition: none;
  transition: none;
}
.header-navigation.breakpoint-on .nav-menu .main-menu ul li .sub-menu > li > a {
  padding: 10px 20px;
}
.header-navigation.breakpoint-on .nav-menu .main-menu ul li .dd-trigger {
  position: absolute;
  right: 0;
  top: 10px;
  height: 25px;
  width: 25px;
  border-radius: 3px;
  z-index: 2;
  cursor: pointer;
  font-size: 16px;
}
.header-navigation.breakpoint-on .nav-menu .main-menu.menu-on {
  left: 0;
}
.header-navigation.breakpoint-on .navbar-toggler {
  display: block;
}
.header-navigation .navbar-toggler {
  padding: 10px 7px;
  border: 1px solid rgba(0, 0, 0, 0.7);
  background-color: transparent;
  cursor: pointer;
  display: none;
  border-radius: 5px;
}
@media (max-width: 1199px) {
  .header-navigation .navbar-toggler {
    margin-left: 30px;
  }
}
@media (max-width: 575px) {
  .header-navigation .navbar-toggler {
    margin-left: 15px;
  }
}
.header-navigation .navbar-toggler span {
  position: relative;
  border-radius: 3px;
  display: block;
  height: 2px;
  padding: 0;
  width: 30px;
  cursor: pointer;
  display: block;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  background-color: rgba(0, 0, 0, 0.7);
}
.header-navigation .navbar-toggler span:not(:first-child) {
  margin-top: 5px;
}
.header-navigation .navbar-toggler.active span:nth-of-type(1) {
  -webkit-transform: rotate3d(0, 0, 1, 45deg);
          transform: rotate3d(0, 0, 1, 45deg);
  top: 7px;
}
.header-navigation .navbar-toggler.active span:nth-of-type(2) {
  opacity: 0;
}
.header-navigation .navbar-toggler.active span:nth-of-type(3) {
  -webkit-transform: rotate3d(0, 0, 1, -45deg);
          transform: rotate3d(0, 0, 1, -45deg);
  top: -7px;
}

.nav-right-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.nav-right-item .lang-dropdown {
  padding: 0 30px;
}
@media (max-width: 1199px) {
  .nav-right-item .lang-dropdown {
    padding-right: 0;
  }
}
.nav-right-item .lang-dropdown .nice-select {
  background-color: transparent;
  font: 500 20px "Prompt", sans-serif;
  padding-right: 20px;
}
.nav-right-item .lang-dropdown .nice-select:after {
  top: 4px;
}
@media (max-width: 1199px) {
  .nav-right-item .lang-dropdown .nice-select {
    font-size: 16px;
  }
}
.nav-right-item .search-btn {
  cursor: pointer;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
@media (max-width: 1199px) {
  .nav-right-item .search-btn {
    display: none;
  }
}
@media (max-width: 1199px) {
  .header-top-bar {
    display: none;
  }
}
.header-top-bar .site-branding {
  max-width: 200px;
}
.header-top-bar .single-info-item-two {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
@media (max-width: 1199px) {
  .header-top-bar .single-info-item-two {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.header-one.transparent-header {
  top: 11px;
}
@media (max-width: 1199px) {
  .header-one.transparent-header {
    top: 0;
  }
}
.header-one .container-fluid {
  padding-left: 170px;
  padding-right: 170px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .header-one .container-fluid {
    padding-left: 70px;
    padding-right: 70px;
  }
}
@media (max-width: 1199px) {
  .header-one .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }
}
.header-one .header-navigation .primary-menu {
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  border-bottom: 1px solid rgba(217, 217, 217, 0.15);
}
@media (max-width: 1199px) {
  .header-one .header-navigation .primary-menu {
    padding: 20px 0;
  }
}

.header-two .header-navigation .primary-menu {
  padding: 11px 0;
  border-bottom: 1px solid rgba(217, 217, 217, 0.3);
}
@media only screen and (min-width: 1200px) {
  .header-two .header-navigation .nav-menu {
    padding-left: 70px;
  }
}
.header-two .header-navigation .nav-right-item {
  margin-left: auto;
}
.header-three .header-navigation .primary-menu {
  padding: 0 50px 0 32px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  border-radius: 12px;
  position: relative;
  z-index: 2;
  margin-bottom: -55px;
}
@media (max-width: 1199px) {
  .header-three .header-navigation .primary-menu {
    padding: 15px;
    margin-bottom: 0;
    margin: 0 -15px;
    border-radius: 0;
  }
}
.header-four .header-navigation .primary-menu {
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  border-bottom: 1px solid rgba(217, 217, 217, 0.15);
}
@media (max-width: 1199px) {
  .header-four .header-navigation .primary-menu {
    padding: 20px 0;
  }
}

/* Navigation White */
.navigation-white.header-navigation .navbar-toggler {
  border-color: rgba(255, 255, 255, 0.8);
}
/* Header Sticky */
.sticky {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  -webkit-animation: sticky 1.2s;
          animation: sticky 1.2s;
}

@-webkit-keyframes sticky {
  0% {
    top: -200px;
  }
  100% {
    top: 0;
  }
}
@keyframes sticky {
  0% {
    top: -200px;
  }
  100% {
    top: 0;
  }
}
/*---==================
    03. Hero css 
=================----*/
/* Hero Content */
.hero-content h1 {
  font: 600 100px "Prompt", sans-serif;
  line-height: 1.1;
 
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .hero-content h1 {
    font-size: 80px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .hero-content h1 {
    font-size: 75px;
  }
}
@media (max-width: 991px) {
  .hero-content h1 {
    font-size: 55px;
    line-height: 1.3;
  }
}
@media (max-width: 575px) {
  .hero-content h1 {
    font-size: 40px;
    line-height: 50px;
  }
}

/* Hero One */
.hero-wrapper .container-fluid {
  padding-left: 170px;
  padding-right: 170px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .hero-wrapper .container-fluid {
    padding-left: 120px;
    padding-right: 120px;
  }
}
@media (max-width: 1199px) {
  .hero-wrapper .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }
}
.hero-wrapper .single-slider {
  padding: 195px 0 90px;
}
.hero-wrapper .hero-content {
  position: relative;
  padding-left: 30px;
  z-index: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .hero-wrapper .hero-content {
    padding-left: 0;
  }
}
@media (max-width: 1199px) {
  .hero-wrapper .hero-content {
    padding-left: 0;
    text-align: center;
    margin-bottom: 40px;
  }
}
@media (max-width: 991px) {
  .hero-wrapper .hero-content {
    padding-left: 0;
  }
}
.hero-wrapper .hero-content h1 {
  margin-bottom: 25px;
}
.hero-wrapper .hero-content .text-button {
  max-width: 700px;
}
@media (max-width: 1199px) {
  .hero-wrapper .hero-content .text-button {
    margin: auto;
    text-align: left;
  }
}
@media (max-width: 767px) {
  .hero-wrapper .hero-content .text-button {
    text-align: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
.hero-wrapper .hero-content .text-button p {
  max-width: 290px;
}
.hero-wrapper .hero-content .text-button .hero-button {
  margin-left: 70px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .hero-wrapper .hero-content .text-button .hero-button {
    margin-left: 30px;
  }
}
@media (max-width: 767px) {
  .hero-wrapper .hero-content .text-button .hero-button {
    margin-left: 0px;
    margin-top: 20px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .hero-wrapper .hero-image {
    margin-left: 0;
  }
}
@media (max-width: 1199px) {
  .hero-wrapper .hero-image {
    text-align: center;
  }
}
.hero-wrapper .hero-image img {
  border-radius: 12px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .hero-wrapper .hero-image img {
    max-width: 100%;
  }
}
@media (max-width: 1199px) {
  .hero-wrapper .hero-image img {
    max-width: 100%;
    margin: auto;
  }
}

/* Hero Two */
.hero-wrapper-two .single-slider {
  padding: 355px 0 270px;
  position: relative;
  z-index: 1;
}
@media (max-width: 767px) {
  .hero-wrapper-two .single-slider {
    padding: 255px 0 220px;
  }
}
.hero-wrapper-two .single-slider .image-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-transition: all 1.4s cubic-bezier(0.7, 0, 0.3, 1);
  transition: all 1.4s cubic-bezier(0.7, 0, 0.3, 1);
  -webkit-transform: scale(1.2);
  transform: scale(1.2);
  z-index: -1;
}
.hero-wrapper-two .single-slider .image-layer:after {
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  width: 100%;
  height: 100%;
  background: linear-gradient(129.29deg, rgba(0, 0, 0, 0.6) 16.77%, rgba(0, 0, 0, 0.3) 74.33%);
  z-index: -1;
}
.hero-wrapper-two .single-slider.slick-current .image-layer {
  -webkit-transform: scale(1);
  transform: scale(1);
}
.hero-wrapper-two .hero-content {
  position: relative;
}
.hero-wrapper-two .hero-content .ribbon {
  border-radius: 10px;
  margin-bottom: 20px;
}
.hero-wrapper-two .hero-content h1 {
  text-transform: capitalize;
  margin-bottom: 30px;
}
/* Hero Three */
.hero-wrapper-three {
  position: relative;
}
.hero-wrapper-three .hero-arrows {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 16%;
  z-index: 2;
}
.hero-wrapper-three .hero-arrows .slick-arrow {
  width: 65px;
  height: 65px;
  border-radius: 50%;
  border: 1px solid #f0f9ff;
  cursor: pointer;
  font-size: 18px;
  margin-bottom: 20px;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.hero-wrapper-three .hero-arrows .slick-arrow.next {
  margin-bottom: 0;
}
.hero-wrapper-three .single-slider {
  padding: 85px 0 140px;
  position: relative;
  z-index: 1;
}
@media (max-width: 1199px) {
  .hero-wrapper-three .single-slider {
    padding: 150px 0;
  }
}
.hero-wrapper-three .single-slider .image-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-transition: all 1.4s cubic-bezier(0.7, 0, 0.3, 1);
  transition: all 1.4s cubic-bezier(0.7, 0, 0.3, 1);
  -webkit-transform: scale(1.2);
  transform: scale(1.2);
  z-index: -1;
}
.hero-wrapper-three .single-slider .image-layer:after {
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  width: 100%;
  height: 100%;
  background: linear-gradient(129.29deg, rgba(0, 0, 0, 0.6) 16.77%, rgba(0, 0, 0, 0.3) 74.33%);
  z-index: -1;
}
.hero-wrapper-three .single-slider.slick-current .image-layer {
  -webkit-transform: scale(1);
  transform: scale(1);
}
.hero-wrapper-three .hero-content span.sub-title {
  margin-bottom: 22px;
}
.hero-wrapper-three .hero-content h1 {
  text-transform: capitalize;
  margin-bottom: 50px;
}
.hero-wrapper-three .hero-content .hero-button .main-btn {
  margin-bottom: 10px;
}
.hero-wrapper-three .hero-content .hero-button .main-btn:not(:last-child) {
  margin-right: 10px;
}

/* Hero Four */
.hero-wrapper-four {
  padding: 180px 0 100px;
}
.hero-wrapper-four .shape {
  position: absolute;
  top: 0;
  left: 0;
}
.hero-wrapper-four .hero-content span.sub-title {
  margin-bottom: 30px;
}
.hero-wrapper-four .hero-content h1 {
  margin-bottom: 35px;
}
.hero-wrapper-four .hero-content p {
  font-size: 18px;
  line-height: 28px;
  padding-left: 25px;
  border-left: 3px solid #ff9d00;
  margin-bottom: 55px;
}
.hero-wrapper-four .hero-content .hero-search-form {
  position: relative;
  z-index: 1;
  margin-right: -434px;
}
@media (max-width: 1199px) {
  .hero-wrapper-four .hero-content .hero-search-form {
    margin-right: 0;
  }
}
.hero-wrapper-four .hero-content .avatar-box img {
  margin-right: 15px;
  margin-bottom: 20px;
}
@media (max-width: 575px) {
  .hero-wrapper-four .hero-content .avatar-box img {
    margin-right: 10px;
  }
}
.hero-wrapper-four .hero-content .avatar-box span {
  margin-bottom: 20px;
}
.hero-wrapper-four .hero-image {
  margin-right: -165px;
  height: 100%;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .hero-wrapper-four .hero-image {
    margin-right: 30px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .hero-wrapper-four .hero-image {
    margin-right: 30px;
  }
}
.hero-wrapper-four .hero-image img {
  width: 100%;
  height: 100%;
  border-radius: 287.5px 287.5px 0px 0px;
  -o-object-fit: cover;
     object-fit: cover;
}

.hero-search-form {
  padding: 30px 30px;
  position: relative;
  border-radius: 10px;
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
}
.hero-search-form .booking-form-two {
  gap: 20px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media (max-width: 1199px) {
  .hero-search-form .booking-form-two {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
.hero-search-form .booking-form-two .form_group {
  width: 18%;
}
@media (max-width: 1199px) {
  .hero-search-form .booking-form-two .form_group {
    width: 48%;
  }
}
@media (max-width: 767px) {
  .hero-search-form .booking-form-two .form_group {
    width: 100%;
  }
}
.hero-search-form .booking-form-two .booking-btn {
  padding: 17px 19px;
}

/* Page Banner  */
.page-banner .page-banner-content h1 {
  margin-bottom: 20px;
}
@media (max-width: 1199px) {
  .page-banner .page-banner-content h1 {
    font-size: 52px;
  }
}
@media (max-width: 767px) {
  .page-banner .page-banner-content h1 {
    font-size: 32px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .page-banner .page-banner-content h1 {
    font-size: 42px;
  }
}
.page-banner .page-banner-content ul.breadcrumb-link li {
  display: inline-block;
  font-size: 24px;
}
@media (max-width: 767px) {
  .page-banner .page-banner-content ul.breadcrumb-link li {
    font-size: 17px;
  }
}
.page-banner .page-banner-content ul.breadcrumb-link li:not(:last-child):after {
  display: inline-block;
  content: "";
  font-weight: 400;
  font-family: "Font Awesome 5 Pro";
  margin-left: 20px;
  margin-right: 13px;
}
.page-banner .page-banner-content ul.breadcrumb-link li.active {
  text-decoration: underline;
}

/*---==================
    04. About css 
=================----*/
.about-two_content-box > p {
  margin-bottom: 45px;
}
.about-two_content-box .card-list {
  max-width: 500px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.about-two_content-box .card-list .card-item {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 14px 20px;
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.15);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
  margin-bottom: 15px;
  font: 500 18px "Prompt", sans-serif;
  margin-right: 18px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-two_content-box .card-list .card-item {
    margin-right: 10px;
  }
}
.about-two_content-box .card-list .card-item i {
  margin-right: 15px;
  font-size: 28px;
}
.about-two_content-box .card-list .card-item:nth-child(1) {
  -webkit-transform: rotate(-7.59deg);
  transform: rotate(-7.59deg);
}
@media (max-width: 767px) {
  .about-two_content-box .card-list .card-item:nth-child(1) {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}
.about-two_content-box .card-list .card-item:nth-child(2) {
  -webkit-transform: rotate(2.25deg);
  transform: rotate(2.25deg);
}
@media (max-width: 767px) {
  .about-two_content-box .card-list .card-item:nth-child(2) {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}
.about-two_content-box .card-list .card-item:nth-child(3) {
  -webkit-transform: rotate(-8.38deg);
  transform: rotate(-8.38deg);
  margin-left: 30px;
}
@media (max-width: 767px) {
  .about-two_content-box .card-list .card-item:nth-child(3) {
    margin-left: 0;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}
.about-two_content-box .card-list .card-item:nth-child(4) {
  -webkit-transform: rotate(1.92deg);
  transform: rotate(1.92deg);
}
@media (max-width: 767px) {
  .about-two_content-box .card-list .card-item:nth-child(4) {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}
.about-two_content-box .card-list .card-item:nth-child(5) {
  -webkit-transform: rotate(-8.06deg);
  transform: rotate(-8.06deg);
}
@media (max-width: 767px) {
  .about-two_content-box .card-list .card-item:nth-child(5) {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}
.about-two_content-box .card-list .card-item:nth-child(6) {
  -webkit-transform: rotate(3.34deg);
  transform: rotate(3.34deg);
}
@media (max-width: 767px) {
  .about-two_content-box .card-list .card-item:nth-child(6) {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}

/*---==================
    05. Elements css 
=================----*/
/* Features Item */
.single-features-item:hover .img-holder .content p {
  opacity: 1;
  max-height: 90px;
  margin-top: 20px;
}
.single-features-item .img-holder {
  position: relative;
}
.single-features-item .img-holder img {
  width: 100%;
  border-radius: 7px;
}
.single-features-item .img-holder .content {
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
  padding: 35px 25px 25px;
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .single-features-item .img-holder .content {
    padding: 35px 25px 25px;
  }
}
.single-features-item .img-holder .text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.single-features-item .img-holder .text h4.title {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 70%;
}
.single-features-item .img-holder .text h4.title:before {
  display: block;
  content: "";
  width: 45px;
  height: 3px;
  margin-bottom: 15px;
  border-radius: 10px;
}
@media (max-width: 1199px) {
  .single-features-item .img-holder .text h4.title {
    font-size: 16px;
  }
}
.single-features-item .img-holder .text .icon-btn {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  -ms-flex: 0 0 auto;
      flex: 0 0 auto;
  width: 45px;
  height: 45px;
  border-radius: 50%;
}
@media (max-width: 575px) {
  .single-features-item .img-holder .text .icon-btn {
    width: 40px;
    height: 40px;
    font-size: 13px;
  }
}
.single-features-item .img-holder p {
  height: auto;
  max-height: 0;
  opacity: 0;
  -webkit-transition: all 0.4s ease-out 0s;
  transition: all 0.4s ease-out 0s;
}
@media (max-width: 1199px) {
  .single-features-item .img-holder p {
    font-size: 14px;
  }
}

.single-features-item-two:hover .img-holder img {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}
.single-features-item-two .img-holder {
  position: relative;
  overflow: hidden;
  border-radius: 20px;
}
.single-features-item-two .img-holder img {
  width: 100%;
  -webkit-transform: scale(1);
  transform: scale(1);
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.single-features-item-two .item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 40px 30px 35px;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(43.14%, rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.8)));
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 43.14%, rgba(0, 0, 0, 0.8) 100%);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.single-features-item-two .content h3.title {
  font-weight: 500;
}

.single-features-item-three .img-holder img {
  width: 100%;
  border-radius: 7px 7px 0 0;
}
.single-features-item-three .content {
  padding: 20px 25px;
  border-radius: 0 0 7px 7px;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.single-features-item-three .content h6 {
  font: 500 16px "Prompt", sans-serif;
  line-height: 21px;
}

.single-features-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
@media (max-width: 575px) {
  .single-features-list {
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
}
.single-features-list .icon-inner .icon-check {
  line-height: 1;
}
.single-features-list .icon-inner .icon-check i {
  font-size: 24px;
}
.single-features-list .icon-inner .icon {
  position: relative;
  margin-left: 55px;
  margin-right: 35px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  font-size: 40px;
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
  z-index: 1;
}
@media (max-width: 575px) {
  .single-features-list .icon-inner .icon {
    margin-left: 20px;
    margin-right: 15px;
  }
}
.single-features-list .icon-inner .icon:after {
  position: absolute;
  content: "";
  width: 2px;
  height: 100%;
  border: 1px dashed #ccc;
  top: 100%;
  z-index: -1;
}
.single-features-list .content h4 {
  margin-bottom: 5px;
}
@media (max-width: 575px) {
  .single-features-list .content h4 {
    font-size: 20px;
  }
}
.single-features-list .content p {
  line-height: 25.6px;
}

/* Fancy Icon Box */
.fancy-icon-box {
  padding: 40px 30px 32px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border: 1px solid transparent;
  border-radius: 7px;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
@media (max-width: 575px) {
  .fancy-icon-box {
    padding: 40px 20px 32px;
  }
}
.fancy-icon-box:hover {
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
  border-color: #ff9d00;
}
.fancy-icon-box .icon {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 75px;
  height: 75px;
  border-radius: 50%;
  font-size: 35px;
  line-height: 1;
  margin-right: 20px;
  -webkit-transition: all 0.4s ease-out 0s;
  transition: all 0.4s ease-out 0s;
}
@media (max-width: 575px) {
  .fancy-icon-box .icon {
    width: 55px;
    height: 55px;
    font-size: 24px;
    margin-right: 15px;
  }
}
.fancy-icon-box .text h4.title {
  margin-bottom: 6px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .fancy-icon-box .text h4.title {
    font-size: 20px;
  }
}

.fancy-icon-box-two {
  padding: 40px 30px 30px;
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.fancy-icon-box-two .icon {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-bottom: 28px;
  font-size: 50px;
  -webkit-transition: all 0.4s ease-out 0s;
  transition: all 0.4s ease-out 0s;
}
.fancy-icon-box-two .text h3.title {
  margin-bottom: 10px;
}

.fancy-icon-box-three {
  border-radius: 65px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 10px 50px 10px 10px;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.fancy-icon-box-three:hover .text h5.title {
  border-color: #f0f9ff;
}
.fancy-icon-box-three .icon {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 110px;
  height: 110px;
  border-radius: 50%;
  font-size: 60px;
  margin-right: 30px;
}
@media (max-width: 767px) {
  .fancy-icon-box-three .icon {
    margin-right: 20px;
  }
}
.fancy-icon-box-three .text h5.title {
  font-weight: 500;
  padding-bottom: 13px;
  border-bottom: 1px solid #1D231F;
  margin-bottom: 7px;
}
.fancy-icon-box-three .text .btn-link {
  text-decoration: underline;
}

.fancy-icon-box-four {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 30px;
  border-radius: 10px;
  background-color: #f8f3fc;
}
.fancy-icon-box-four .icon {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 35px;
  margin-right: 20px;
}
.fancy-icon-box-four .icon i {
  font-size: 35px;
}
.fancy-icon-box-four .text {
  margin-top: -7px;
}
.fancy-icon-box-four .text h4.title {
  margin-bottom: 5px;
  font-weight: 500;
}
.fancy-icon-box-four .text p {
  line-height: 18px;
  font-size: 14px;
}

/* Single Team Item */
.single-team-item {
  border-radius: 7px;
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
  padding: 35px 30px 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.single-team-item .member-img {
  width: 90px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  margin-right: 20px;
}
.single-team-item .member-img img {
  width: 100%;
  border-radius: 50%;
}
.single-team-item .member-info ul.social-link {
  padding-top: 20px;
  border-top: 1px solid rgba(29, 35, 31, 0.1);
  margin-top: 20px;
  line-height: 1;
}
.single-team-item .member-info ul.social-link li:not(:last-child) {
  margin-right: 15px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .single-team-item .member-info h4.title {
    font-size: 19px;
  }
}
@media (max-width: 991px) {
  .single-team-item .member-info h4.title {
    font-size: 17px;
  }
}
@media (max-width: 991px) {
  .single-team-item .member-info p.position {
    font-size: 14px;
  }
}

/* Single Counter Item */
@media (max-width: 767px) {
  .counter-item {
    text-align: center;
  }
}
.counter-item h2.number {
  font: 500 55px "Prompt", sans-serif;
  line-height: 1;
  margin-bottom: 5px;
}
.counter-item p {
  font: 400 16px "Prompt", sans-serif;
}

.single-counter-item {
  padding: 40px 30px 27px;
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.05);
  border-radius: 7px;
  border: 1px solid transparent;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.single-counter-item:hover {
  border-color: #000;
}
.single-counter-item .icon {
  width: 145px;
  height: 145px;
  border-radius: 50%;
  background-color: rgba(99, 171, 69, 0.1);
  font-size: 70px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-bottom: 35px;
  -webkit-transition: all 0.4s ease-out 0s;
  transition: all 0.4s ease-out 0s;
}
.single-counter-item h2.number {
  font: 500 45px "Prompt", sans-serif;
  line-height: 55px;
}

.single-counter-item-two {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.single-counter-item-two .icon {
  width: 95px;
  height: 95px;
  border-radius: 50%;
  margin-bottom: 25px;
  font-size: 50px;
}
.single-counter-item-two .content h2.number {
  font: 500 55px "Prompt", sans-serif;
  line-height: 1;
  margin-bottom: 5px;
}
.single-counter-item-two .content p {
  font-weight: 400;
}

/* Single Activity Item */
.single-activity-item .img-holder img {
  width: 100%;
  border-radius: 20px 20px 0px 0px;
}
.single-activity-item .content {
  border-radius: 0px 0px 20px 20px;
  padding: 0 40px 40px;
}
.single-activity-item .content .meta {
  position: relative;
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
  border-radius: 10px;
  margin: -35px 0 35px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 21px 40px;
}
@media (max-width: 991px) {
  .single-activity-item .content .meta {
    padding: 21px 30px;
  }
}
.single-activity-item .content .meta ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  line-height: 0;
}
.single-activity-item .content .meta ul li {
  display: inline-block;
}
.single-activity-item .content .meta ul li:not(:last-child) {
  margin-right: 30px;
}
@media (max-width: 991px) {
  .single-activity-item .content .meta ul li:not(:last-child) {
    margin-right: 20px;
  }
}
.single-activity-item .content .meta ul li span {
  font-size: 20px;
}
@media (max-width: 991px) {
  .single-activity-item .content .meta ul li span {
    font-size: 16px;
  }
}
.single-activity-item .content .meta .rate {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font: 500 16px "Prompt", sans-serif;
  color: #000;
}
.single-activity-item .content .meta .rate i {
  margin-right: 5px;
}
.single-activity-item .content h3.title {
  margin-bottom: 9px;
}
.single-activity-item .content p {
  margin-bottom: 20px;
}
/* Single Event Item */
.single-event-item {
  padding: 15px;
  -webkit-box-shadow: 0px 7px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 7px 60px rgba(0, 0, 0, 0.07);
  border-radius: 12px;
}
.single-event-item .img-holder img {
  width: 100%;
  border-radius: 12px;
}
.single-event-item .content {
  padding: 30px 35px 10px;
}
.single-event-item .content .meta span {
  margin-bottom: 10px;
  font-weight: 400;
  text-transform: uppercase;
}
.single-event-item .content .meta span i {
  margin-right: 5px;
}
.single-event-item .content h3.title {
  margin-bottom: 25px;
  padding-bottom: 27px;
  font-weight: 500;
  border-bottom: 1px solid rgba(29, 35, 31, 0.1);
}
@media (max-width: 991px) {
  .single-event-item .content h3.title {
    font-size: 18px;
    line-height: 30px;
  }
}
.single-event-item .content p.location {
  font-size: 18px;
}
@media (max-width: 991px) {
  .single-event-item .content p.location {
    font-size: 14px;
  }
}

/* Single CTA Item */
.single-cta-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.single-cta-item .icon {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 85px;
  margin-right: 30px;
}
.single-cta-item .content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
}
.single-cta-item .content h3.title {
  margin-right: 100px;
  line-height: 1.5;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .single-cta-item .content h3.title {
    margin-right: 40px;
  }
}
@media (max-width: 1199px) {
  .single-cta-item .content h3.title {
    font-size: 22px;
    margin-right: 30px;
  }
}
@media (max-width: 575px) {
  .single-cta-item .content h3.title {
    font-size: 18px;
    line-height: 30px;
  }
}
.single-cta-item .content .icon-btn {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 65px;
  height: 65px;
  border-radius: 50%;
  -webkit-box-shadow: 0px 10px 60px rgba(99, 171, 69, 0.65);
          box-shadow: 0px 10px 60px rgba(99, 171, 69, 0.65);
}
.single-cta-item .content .icon-btn i {
  font-size: 27px;
  -webkit-transform: rotate(-38.07deg);
  transform: rotate(-38.07deg);
  margin-top: 5px;
}
@media (max-width: 575px) {
  .single-cta-item .content .icon-btn {
    display: none;
  }
}

/* Single Info Item */
.single-info-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.single-info-item .icon {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 18px;
  margin-right: 15px;
}
.single-info-item .info span.title {
  font: 500 16px "Prompt", sans-serif;
  line-height: 26px;
}
.single-info-item .info p {
  line-height: 21px;
}

.single-info-item-two {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.single-info-item-two .inner-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.single-info-item-two .icon {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid rgba(29, 35, 31, 0.1);
  margin-right: 20px;
}
.single-info-item-two .info span.title {
  font-weight: 400;
  line-height: 20px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .single-info-item-two .info span.title {
    font-size: 12px;
  }
}
.single-info-item-two .info h5 {
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .single-info-item-two .info h5 {
    font-size: 18px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .single-info-item-two .info h5 {
    font-size: 14px;
  }
}
/* Progress Bar */
.single-progress-bar .progress-title h6 {
  margin-bottom: 10px;
}
.single-progress-bar .progress-title h6 span {
  float: right;
}
.single-progress-bar .progress {
  height: 5px;
  border-radius: 0;
  background-color: rgba(29, 35, 31, 0.1);
}
.single-skill-circle {
  position: relative;
  padding: 0 40px;
}
@media (max-width: 1199px) {
  .single-skill-circle {
    padding: 0 30px;
  }
}
@media (max-width: 575px) {
  .single-skill-circle:not(:last-child) {
    margin-bottom: 30px;
  }
}
.single-skill-circle:not(:last-child):after {
  position: absolute;
  right: 0;
  top: 0;
  content: "";
  width: 1px;
  height: 100%;
  background-color: #D9D9D9;
}
@media (max-width: 575px) {
  .single-skill-circle:not(:last-child):after {
    display: none;
  }
}
.single-skill-circle .inner-circle {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-bottom: 20px;
  overflow: hidden;
}
.single-skill-circle .inner-circle .number {
  font: 500 27px "Prompt", sans-serif;
}
.single-skill-circle .inner-circle .line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 10px solid #ff9d00;
  border-color: transparent #ff9d00 #ff9d00;
  -webkit-transform: rotate(55deg);
  transform: rotate(55deg);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .single-skill-circle h5 {
    font-size: 18px;
  }
}

/* Partners Item */
.single-partner-item .partner-img {
  padding: 0 20px;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

/*--==== Pagination Css ====--*/
.gowilds-pagination li {
  display: inline-block;
}
.gowilds-pagination li a {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  border: 2px solid rgba(29, 35, 31, 0.1);
  font: 500 16px "Prompt", sans-serif;
  margin-bottom: 10px;
}
@media (max-width: 767px) {
  .gowilds-pagination li a {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }
}
.gowilds-pagination li a.active, .gowilds-pagination li a:hover {
  border-color: transparent;
}

/*---==================
    05. Features css 
=================----*/
.ratings li {
  display: inline-block;
}
.ribbon {
  font: 500 18px "Prompt", sans-serif;
  padding: 10px 20px;
}

.radius-60 {
  border-radius: 60px;
}

.radius-12 {
  border-radius: 12px;
}

.radius-top-left-right-288 {
  border-radius: 288px 288px 0px 0px;
}

.video-popup {
  position: relative;
  width: 110px;
  height: 110px;
  font-size: 14px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border-radius: 50%;
}
.video-popup:after, .video-popup:before {
  position: absolute;
  left: 0;
  top: 0;
  content: "";
  border-radius: 50%;
  width: 100%;
  height: 100%;
  border: 1px solid #e1e1e1;
  -webkit-animation: playpopup infinite linear 1s;
          animation: playpopup infinite linear 1s;
}
.video-popup:before {
  -webkit-animation: playpopup infinite linear 2s;
          animation: playpopup infinite linear 2s;
}

@-webkit-keyframes playpopup {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 0.6;
  }
  50% {
    -webkit-transform: scale(1.3);
            transform: scale(1.3);
    opacity: 0.3;
  }
  100% {
    -webkit-transform: scale(1.5);
            transform: scale(1.5);
    opacity: 0;
  }
}
@keyframes playpopup {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 0.6;
  }
  50% {
    -webkit-transform: scale(1.5);
            transform: scale(1.5);
    opacity: 0.3;
  }
  100% {
    -webkit-transform: scale(2);
            transform: scale(2);
    opacity: 0;
  }
}
.check-list li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font: 400 18px "Prompt", sans-serif;
}
.check-list li:not(:last-child) {
  margin-bottom: 20px;
}
.check-list li i {
  margin-right: 15px;
  font-size: 20px;
}

.features-one_image-box {
  margin-left: -90px;
}
@media (max-width: 991px) {
  .features-one_image-box {
    margin-left: 0;
    text-align: center;
  }
}
.features-one_image-box img {
  border-radius: 305px 305px 0px 0px;
}

.features-item-area > .row .col-md-6:nth-child(2) {
  margin-top: -30px;
}
@media (max-width: 767px) {
  .features-item-area > .row .col-md-6:nth-child(2) {
    margin-top: 0;
  }
}
.features-item-area > .row .col-md-6:nth-child(4) {
  margin-top: -30px;
}
@media (max-width: 767px) {
  .features-item-area > .row .col-md-6:nth-child(4) {
    margin-top: 0;
  }
}

.features-list_one .single-features-list:last-child .icon-inner .icon:after {
  display: none;
}

/*---==================
    07. Services css 
=================----*/
.single-service-item {
  padding: 20px 20px 20px;
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
  border-radius: 10px;
  border: 1px solid transparent;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.single-service-item:hover {
  border-color: #ff9d00;
}
.single-service-item .content {
  position: relative;
  padding: 20px;
}
.single-service-item .content h3.title {
  margin-bottom: 7px;
}
.single-service-item .content p {
  margin-bottom: 15px;
}
.single-service-item .content .meta span {
  line-height: 1;
}
.single-service-item .content .meta span:not(:last-child) {
  margin-right: 30px;
}
@media (max-width: 1199px) {
  .single-service-item .content .meta span:not(:last-child) {
    margin-right: 15px;
  }
}
.single-service-item .content .meta span i {
  font-size: 24px;
}
.single-service-item .content .icon-btn {
  position: absolute;
  bottom: -27px;
  right: 20px;
  width: 55px;
  height: 55px;
  border-radius: 50%;
  font-size: 18px;
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.15);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.15);
}
.single-service-item .img-holder img {
  width: 100%;
  border-radius: 10px;
}

.single-service-item-two {
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  z-index: 1;
  background-color: #4d3b01;
  padding: 50px 50px 40px;
}
@media (max-width: 991px) {
  .single-service-item-two {
    padding: 50px 30px 40px;
  }
}
.single-service-item-two:hover .hover-bg {
  visibility: visible;
  opacity: 1;
}
.single-service-item-two .hover-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.4s ease-out 0s;
  transition: all 0.4s ease-out 0s;
}
.single-service-item-two .hover-bg:after {
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  width: 100%;
  height: 100%;
  background-color: rgba(16, 19, 17, 0.75);
  z-index: -1;
}
.single-service-item-two .content .icon {
  margin-bottom: 25px;
}
.single-service-item-two .content .icon i {
  font-size: 80px;
}
.single-service-item-two .content h3.title {
  margin-bottom: 15px;
}
.single-service-item-two .content p {
  margin-bottom: 25px;
}
.single-service-item-three {
  padding: 35px 30px 40px;
  border-radius: 10px;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.single-service-item-three:hover {
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
}
.single-service-item-three .content h3.title {
  margin-bottom: 10px;
}
.single-service-item-three .content p {
  margin-bottom: 23px;
}
.single-service-item-three .content img {
  width: 100%;
  border-radius: 10px;
  margin-bottom: 20px;
}
.single-service-item-three .content .meta {
  border-top: rgba(29, 35, 31, 0.1);
  padding-top: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.single-service-item-three .content .meta span {
  line-height: 1;
}
.single-service-item-three .content .meta span.icon i {
  font-size: 24px;
}
.single-service-item-three .content .meta span.icon:not(:last-child) {
  margin-right: 30px;
}
@media (max-width: 991px) {
  .single-service-item-three .content .meta span.icon:not(:last-child) {
    margin-right: 20px;
  }
}
.single-service-item-three .content .meta span.rate {
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
  border-radius: 7px;
  font-weight: 500;
  padding: 10px 13px;
  line-height: 14px;
  margin-left: auto;
}
.single-service-item-three .content .meta span.rate i {
  margin-right: 3px;
}

.single-service-item-four {
  padding: 10px 10px 20px;
  background-color: #101311;
  border-radius: 10px;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.single-service-item-four:hover .content p {
  color: #484848;
}
.single-service-item-four:hover .content .meta span {
  color: #484848;
}
.single-service-item-four:hover .content .action-btn {
  border-color: rgba(29, 35, 31, 0.1);
}
.single-service-item-four .img-holder img {
  width: 100%;
  border-radius: 10px;
}
.single-service-item-four .content {
  position: relative;
  padding: 30px 20px 0;
}
.single-service-item-four .content .icon-btn {
  position: absolute;
  right: 20px;
  top: -28px;
  width: 55px;
  height: 55px;
  border-radius: 50%;
  font-size: 18px;
}
.single-service-item-four .content h3.title {
  margin-bottom: 10px;
}
.single-service-item-four .content p {
  color: rgba(255, 255, 255, 0.65);
  margin-bottom: 18px;
}
.single-service-item-four .content .meta {
  padding-bottom: 30px;
}
.single-service-item-four .content .meta span {
  color: rgba(255, 255, 255, 0.65);
  font-size: 28px;
}
.single-service-item-four .content .meta span:not(:first-child) {
  margin-left: 35px;
}
.single-service-item-four .content .action-btn {
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/*---==================
    08. Activity css 
=================----*/
.activity-wrapper-bgc {
  padding: 100px 30px 50px;
}
@media (max-width: 767px) {
  .activity-wrapper-bgc {
    padding: 100px 0 50px;
  }
}

.activity-nav-tab {
  padding: 40px 5px 25px;
  border-radius: 12px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .activity-nav-tab {
    padding: 40px 15px 25px;
  }
}
.activity-nav-tab .nav-tabs {
  display: flow-root;
  border-bottom: none;
}
.activity-nav-tab .nav-tabs li {
  display: inline-block;
}
.activity-nav-tab .nav-tabs li a {
  padding: 3px 8px;
  font: 500 12px "Prompt", sans-serif;
  margin-bottom: 15px;
  border-radius: 12px;
  border: none;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .activity-nav-tab .nav-tabs li a {
    padding: 10px 15px;
  }
}

@media (max-width: 767px) {
  .activity-content-box {
    margin-bottom: 40px;
  }
}
.activity-content-box .icon {
  margin-bottom: 10px;
}
.activity-content-box .icon i {
  font-size: 85px;
}
.activity-content-box h3.title {
  font: 500 27px "Prompt", sans-serif;
  margin-bottom: 25px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .activity-content-box h3.title {
    font-size: 22px;
  }
}
.activity-content-box p {
  margin-bottom: 30px;
}
/*---==================
    09. We css 
=================----*/
@media (max-width: 991px) {
  .we-one_content-box {
    margin-bottom: 50px;
    text-align: center;
  }
}
.we-one_content-box p {
  margin-bottom: 30px;
}

.we-two_image-box {
  max-width: 630px;
}
@media (max-width: 1199px) {
  .we-two_image-box {
    margin: auto;
  }
}
.we-two_image-box .we-image img {
  border-radius: 10px;
}

/* CTA CSS */
.cta-image img {
  border-radius: 10px;
}

@media (max-width: 991px) {
  .cta-content-box {
    text-align: center;
    margin-bottom: 80px;
  }
}

/*---==================
    10. Gallery css 
=================----*/
.single-gallery-item:hover .gallery-img .hover-overlay {
  visibility: visible;
  opacity: 1;
}
.single-gallery-item:hover .gallery-img .icon-btn {
  -webkit-transform: translateY(0px);
  transform: translateY(0px);
}
.single-gallery-item .gallery-img {
  position: relative;
  overflow: hidden;
}
.single-gallery-item .gallery-img img {
  width: 100%;
  border-radius: 7px;
}
.single-gallery-item .gallery-img .hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 7px;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.single-gallery-item .gallery-img .icon-btn {
  width: 65px;
  height: 65px;
  border-radius: 50%;
  font-size: 18px;
  -webkit-transition: all 0.4s ease-out 0s;
  transition: all 0.4s ease-out 0s;
  -webkit-transform: translateY(20px);
  transform: translateY(20px);
}

.single-gallery-item-two .img-holder img {
  border-radius: 12px;
}
.single-gallery-item-two .content {
  padding: 33px 40px 0;
}
.single-gallery-item-two .content h3.title {
  margin-bottom: 5px;
}

/*---==================
    11. Booking css 
=================----*/
.booking-form-wrapper {
  padding: 33px 60px 40px;
  border-radius: 12px;
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
  margin-top: -85px;
  position: relative;
}
@media (max-width: 767px) {
  .booking-form-wrapper {
    padding: 33px 30px 40px;
  }
}

.booking-form label {
  position: absolute;
  top: 18px;
  right: 23px;
}
.booking-form label i {
  font-size: 18px;
}
.booking-form .nice-select, .booking-form .form_control {
  padding: 19px 25px;
  border: 1px solid rgba(29, 35, 31, 0.1);
  border-radius: 7px;
  margin-bottom: 20px;
  line-height: 25px;
  font-weight: 500;
  color: #000;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.booking-form .nice-select.placeholder, .booking-form .form_control.placeholder {
  color: #000;
}
.booking-form .nice-select:-moz-placeholder, .booking-form .form_control:-moz-placeholder {
  color: #000;
}
.booking-form .nice-select::-moz-placeholder, .booking-form .form_control::-moz-placeholder {
  color: #000;
}
.booking-form .nice-select::-webkit-input-placeholder, .booking-form .form_control::-webkit-input-placeholder {
  color: #000;
}
.booking-form .nice-select:focus, .booking-form .form_control:focus {
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
  border-color: transparent;
}
.booking-form .nice-select:after {
  right: 25px;
  font-size: 18px;
}

.booking-form-two {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .booking-form-two {
    gap: 15px;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
}
@media (max-width: 1199px) {
  .booking-form-two {
    gap: 15px;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
.booking-form-two .form_group {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 18.5%;
}
@media (max-width: 1199px) {
  .booking-form-two .form_group {
    width: 48%;
  }
}
@media (max-width: 991px) {
  .booking-form-two .form_group {
    width: 100%;
  }
}
.booking-form-two .form_group > span {
  font: 500 18px "Prompt", sans-serif;
  margin-bottom: 10px;
}
.booking-form-two .form_group label {
  position: absolute;
  top: 55px;
  right: 20px;
}
.booking-form-two .nice-select, .booking-form-two .form_control {
  padding: 16px 25px;
  line-height: 26px;
  padding: 16px 25px;
  border: 1px solid rgba(28, 35, 31, 0.15);
  border-radius: 50px;
}
.booking-form-two .nice-select:after {
  right: 20px;
}
.booking-form-two .booking-btn {
  width: 100%;
  border: 1px solid rgba(28, 35, 31, 0.15);
  background-color: transparent;
  padding: 17px 30px;
  border-radius: 50px;
  line-height: 25px;
  font: 600 14px "Prompt", sans-serif;
  text-transform: uppercase;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.booking-form-two .booking-btn:hover {
  border-color: transparent;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .booking-form-two .booking-btn {
    padding: 17px 15px;
  }
}

/*---==================
    02. Places css 
=================----*/
.place-slider {
  margin-left: -15px;
  margin-right: -15px;
}
.place-slider .slick-slide {
  margin-left: 15px;
  margin-right: 15px;
}

.related-tour-place .place-arrows {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
@media (max-width: 767px) {
  .related-tour-place .place-arrows {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
}
.related-tour-place .place-arrows .slick-arrow {
  color: #B8B8B8;
  cursor: pointer;
  font-size: 24px;
  z-index: 1;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.related-tour-place .place-arrows .slick-arrow.prev {
  margin-right: 20px;
}
.single-place-item .place-img img {
  width: 80%; 
  height: auto;
  border-radius: 15px;
  margin-bottom: -5%;
  display: block; /* Ensures margin auto works */
  margin-left: auto; /* Horizontal centering */
  margin-right: auto; /* Horizontal centering */
  
}
.single-place-item .place-content {
  position: relative;
  margin-left: 25px;
  margin-right: 25px;
  /* margin-top: -60px; */
}
@media (max-width: 991px) {
  .single-place-item .place-content {
    margin-left: 15px;
    margin-right: 15px;
  }
}
.single-place-item .place-content .info {
  padding: 30px 40px 28px;
  /* -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.07); */
  border-radius: 15px;
}
@media (max-width: 991px) {
  .single-place-item .place-content .info {
    padding: 30px 20px 28px;
  }
}
.single-place-item .place-content .info ul.ratings {
  margin-bottom: 10px;
}
.single-place-item .place-content .info h4.title {
  line-height: 28.6px;
  margin-bottom: 12px;
}
.single-place-item .place-content .info p i {
  margin-right: 10px;
}
.single-place-item .place-content .info .meta {
  margin-top: 25px;
  padding-top: 28px;
  border-top: 1px solid rgba(29, 35, 31, 0.1);
}
.single-place-item .place-content .info .meta span {
  font: 400 16px "Prompt", sans-serif;
}
.single-place-item .place-content .info .meta span:not(:last-child) {
  margin-right: 25px;
}
@media (max-width: 991px) {
  .single-place-item .place-content .info .meta span:not(:last-child) {
    margin-right: 15px;
  }
}
.single-place-item .place-content .info .meta span i {
  margin-right: 10px;
}
.single-place-item .place-content .info .meta span a {
  font: 500 16px "Prompt", sans-serif;
}
.single-place-item .place-content .info .meta span a i {
  margin-right: 0;
  margin-left: 10px;
}

.single-place-item-two .place-img {
  position: relative;
  overflow: hidden;
  z-index: 1;
}
.single-place-item-two .place-img img {
  width: 100%;
  border-radius: 12px;
}
.single-place-item-two .place-img .item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.75)));
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.75) 100%);
  border-radius: 12px;
}
.single-place-item-two .place-img .tour-count {
  position: absolute;
  top: 20px;
  right: 20px;
  font: 500 14px "Prompt", sans-serif;
  padding: 5px 10px;
  border-radius: 5px;
  line-height: 18px;
}
.single-place-item-two .place-img .place-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.75)));
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.75) 100%);
  border-radius: 12px;
  padding: 25px 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}
.single-place-item-two .place-img .place-content .info p.price {
  font: 600 16px "Prompt", sans-serif;
}

.single-place-item-three {
  padding: 10px 10px 0;
  border-radius: 50px;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.single-place-item-three:hover {
  -webkit-box-shadow: 0px 10px 60px 30px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 10px 60px 30px rgba(0, 0, 0, 0.05);
}
.single-place-item-three .place-img img {
  width: 100%;
  border-radius: 40px;
}
.single-place-item-three .place-content {
  padding: 20px;
}
.single-place-item-three .place-content h4.title {
  font: 500 20px "Prompt", sans-serif;
  line-height: 26px;
  margin-bottom: 5px;
}
.single-place-item-three .place-content p.location {
  margin-bottom: 5px;
}
.single-place-item-three .place-content p.location i {
  margin-right: 5px;
}
.single-place-item-three .place-content .meta {
  padding-top: 15px;
  border-top: 1px solid rgba(29, 35, 31, 0.1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.single-place-item-three .place-content .meta span i {
  margin-right: 5px;
}
.single-place-item-three .place-content .meta .icon-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 14px;
  margin-left: auto;
}

.tour-title-wrapper .tour-title h3.title {
  font-size: 35px;
  line-height: 45px;
  margin-bottom: 10px;
}
.tour-title-wrapper .tour-title p {
  font-weight: 400;
}
.tour-title-wrapper .tour-title p i {
  margin-right: 8px;
}
.tour-title-wrapper .tour-widget-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
@media (max-width: 1199px) {
  .tour-title-wrapper .tour-widget-info {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
}
@media (max-width: 767px) {
  .tour-title-wrapper .tour-widget-info {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
.tour-title-wrapper .tour-widget-info .info-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.tour-title-wrapper .tour-widget-info .info-box:not(:last-child) {
  margin-right: 50px;
}
@media (max-width: 767px) {
  .tour-title-wrapper .tour-widget-info .info-box:not(:last-child) {
    margin-right: 20px;
  }
}
.tour-title-wrapper .tour-widget-info .info-box .icon {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 45px;
  margin-right: 20px;
  font-size: 50px;
}
.tour-title-wrapper .tour-widget-info .info-box .info h4 {
  line-height: 25px;
}
.tour-title-wrapper .tour-widget-info .info-box .info h4 span {
  display: block;
  font-weight: 300;
  font-size: 18px;
}

.tour-area-nav {
  border-top: 1px solid rgba(29, 35, 31, 0.1);
  border-bottom: 1px solid rgba(29, 35, 31, 0.1);
}
.tour-area-nav .share-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
@media (max-width: 767px) {
  .tour-area-nav .share-nav {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    margin-top: 20px;
  }
}
.tour-area-nav .share-nav a {
  padding: 14px 30px;
  background-color: rgba(29, 35, 31, 0.1);
  border-radius: 40px;
  font: 600 14px "Prompt", sans-serif;
  text-transform: uppercase;
  line-height: 21px;
}
.tour-area-nav .share-nav a i {
  margin-left: 5px;
}
.tour-area-nav .share-nav a:not(:last-child) {
  margin-right: 15px;
}
@media (max-width: 767px) {
  .tour-area-nav .share-nav a {
    padding: 14px 10px;
  }
}

.tour-details-wrapper .place-content-wrap h3.title {
  font: 500 38px "Prompt", sans-serif;
  margin-bottom: 15px;
}
@media (max-width: 767px) {
  .tour-details-wrapper .place-content-wrap h3.title {
    font-size: 24px;
    line-height: 35px;
  }
}
.tour-details-wrapper .place-content-wrap > p {
  margin-bottom: 45px;
}
.tour-details-wrapper .place-content-wrap h4 {
  font: 500 27px "Prompt", sans-serif;
  margin-bottom: 5px;
}
.tour-details-wrapper .place-content-wrap .check-list {
  margin-top: -20px;
}
@media (max-width: 991px) {
  .tour-details-wrapper .place-content-wrap .check-list {
    margin-top: 0;
    margin-bottom: 30px;
  }
}
.tour-details-wrapper .map-box iframe {
  height: 475px;
}

.days-area .nav-tabs {
  border-radius: 7px;
  border-bottom: none;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.days-area .nav-tabs .nav-link {
  font: 500 18px "Prompt", sans-serif;
  border: none;
  background-color: transparent;
  color: #919191;
  padding: 15px 30px;
}
.days-area .content-box p {
  margin-bottom: 20px;
}

.destination-details-wrapper .destination-info h3.title {
  font-size: 48px;
  line-height: 56px;
  margin-bottom: 25px;
}
@media (max-width: 991px) {
  .destination-details-wrapper .destination-info h3.title {
    font-size: 27px;
    line-height: 35px;
  }
}
.destination-details-wrapper .destination-info .meta {
  margin-bottom: 10px;
}
.destination-details-wrapper .destination-info .meta span {
  font: 400 18px "Prompt", sans-serif;
  margin-bottom: 10px;
}
.destination-details-wrapper .destination-info .meta span:not(:first-child) {
  margin-left: 20px;
}
.destination-details-wrapper .destination-info .meta span.location i {
  margin-right: 10px;
}
.destination-details-wrapper .destination-info .meta span .ratings {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 7px;
}
.destination-details-wrapper .destination-info .meta span .ratings li i {
  font-size: 14px;
}
.destination-details-wrapper .destination-info .meta span .ratings li a {
  font-size: 22px;
}
.destination-details-wrapper .destination-info p {
  margin-bottom: 38px;
}
.destination-details-wrapper .destination-info h3 {
  margin-bottom: 20px;
}
.destination-details-wrapper .destination-info .features-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.destination-details-wrapper .destination-info .features-list li {
  margin-bottom: 20px;
}
.destination-details-wrapper .destination-info .features-list li:not(:last-child) {
  margin-right: 20px;
}
.destination-details-wrapper .destination-info .features-list li span {
  padding: 15px 20px;
  font: 500 18px "Prompt", sans-serif;
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.15);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
}
.destination-details-wrapper .destination-info .features-list li span i {
  margin-right: 15px;
}

/*---==================
    13. Testimonials css 
=================----*/
.quote-rating-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.quote-rating-box .icon {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 87px;
  -webkit-margin-end: 40px;
          margin-inline-end: 40px;
}
.quote-rating-box .ratings-box h4 {
  margin-bottom: 6px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .quote-rating-box .ratings-box h4 {
    font-size: 20px;
  }
}
@media (max-width: 991px) {
  .quote-rating-box .ratings-box h4 {
    font-size: 18px;
  }
}

.author-thumb-title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.author-thumb-title .author-thumb {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 80px;
  -webkit-margin-end: 20px;
          margin-inline-end: 20px;
}
.author-thumb-title .author-thumb img {
  width: 100%;
  border-radius: 50%;
}
.author-thumb-title .author-title h3 {
  font-size: 27px;
  margin-bottom: 7px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .author-thumb-title .author-title h3 {
    font-size: 24px;
  }
}
@media (max-width: 991px) {
  .author-thumb-title .author-title h3 {
    font-size: 20px;
  }
}

.gw-testimonial-item .testimonial-inner-content .quote-rating-box {
  margin-bottom: 30px;
}
.gw-testimonial-item .testimonial-inner-content > p {
  font: 400 33px "Prompt", sans-serif;
  line-height: 46px;
  margin-bottom: 35px;
}
@media (max-width: 767px) {
  .gw-testimonial-item .testimonial-inner-content > p {
    font-size: 24px;
    line-height: 35px;
  }
}

.gw-testimonial-item-two {
  padding: 40px 30px;
  border-radius: 15px;
}
.gw-testimonial-item-two .testimonial-inner-content .quote-rating-box {
  margin-bottom: 30px;
}
.gw-testimonial-item-two .testimonial-inner-content > p {
  font: 400 18px "Prompt", sans-serif;
  line-height: 30px;
  margin-bottom: 25px;
}

.testimonial-one_image-box {
  margin-left: -44%;
}
@media (max-width: 1199px) {
  .testimonial-one_image-box {
    margin-left: 0;
  }
}
.testimonial-one_image-box img {
  max-width: 753px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .testimonial-one_image-box img {
    max-width: 100%;
  }
}
@media (max-width: 1199px) {
  .testimonial-one_image-box img {
    max-width: 100%;
  }
}

.testimonial-section-two {
  background: url(../images/bg/map2.png) no-repeat;
  background-position: center center;
}

/*---==================
    14. Shop css 
=================----*/
@media (max-width: 767px) {
  .page-item-filter .show-text {
    text-align: center;
  }
}
.page-item-filter .show-text h6 {
  font-weight: 400;
}
.page-item-filter .product-dropdown {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 767px) {
  .page-item-filter .product-dropdown {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
.page-item-filter .product-dropdown span.title {
  font: 400 18px "Prompt", sans-serif;
  color: #000;
  margin-right: 15px;
  margin-bottom: 20px;
}
.page-item-filter .product-dropdown .nice-select {
  border: 1px solid rgba(29, 35, 31, 0.1);
  width: 170px;
  padding: 11px 20px;
  line-height: 24px;
  border-radius: 22px;
  font: 300 14px "Prompt", sans-serif;
  margin-bottom: 20px;
}
.page-item-filter .product-dropdown .nice-select:after {
  right: 20px;
  top: 15px;
}

.single-product-item:hover .img-holder .content-hover {
  visibility: visible;
  opacity: 1;
}
.single-product-item:hover .img-holder .content-hover .main-btn {
  -webkit-transform: translateY(0);
  transform: translateY(0);
}
.single-product-item .img-holder {
  position: relative;
  overflow: hidden;
}
.single-product-item .img-holder img {
  width: 100%;
}
.single-product-item .img-holder .content-hover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 30px;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.4s ease-out 0s;
  transition: all 0.4s ease-out 0s;
}
.single-product-item .img-holder .content-hover .main-btn {
  -webkit-transform: translateY(-20px);
  transform: translateY(-20px);
}
.single-product-item .img-holder .tag {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1;
}
.single-product-item .img-holder .tag span {
  font: 500 14px "Prompt", sans-serif;
  padding: 5px 10px;
  border-radius: 5px;
  line-height: 18px;
  margin-bottom: 7px;
  display: block;
}
.single-product-item .img-holder .tag span.best {
  background-color: #F7451E;
}
.single-product-item .content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-top: 25px;
}
.single-product-item .content .info h4.title {
  font-size: 20px;
  margin-bottom: 5px;
}
.single-product-item .content .info span.price {
  font-weight: 400;
}
.single-product-item .content .info span.price .prev-price {
  font-weight: 300;
  text-decoration: line-through;
  margin-right: 10px;
}
.single-product-item .content .ratings {
  margin-left: auto;
}

.product-gallery-area .product-big-slider .product-img a {
  display: block;
}
.product-gallery-area .product-big-slider .product-img img {
  width: 100%;
  border-radius: 20px;
}
.product-gallery-area .product-thumb-slider {
  margin-left: -15px;
  margin-right: -15px;
  cursor: pointer;
}
@media (max-width: 575px) {
  .product-gallery-area .product-thumb-slider {
    margin-left: -12px;
    margin-right: -12px;
  }
}
.product-gallery-area .product-thumb-slider .product-img {
  margin-left: 15px;
  margin-right: 15px;
}
@media (max-width: 575px) {
  .product-gallery-area .product-thumb-slider .product-img {
    margin-left: 12px;
    margin-right: 12px;
  }
}
.product-gallery-area .product-thumb-slider .product-img img {
  width: 100%;
  border-radius: 10px;
}

/*--==== Product Details ====--*/
.product-details-wrapper .product-info h4.title {
  font-size: 30px;
  margin-bottom: 10px;
}
@media (max-width: 575px) {
  .product-details-wrapper .product-info h4.title {
    font-size: 20px;
  }
}
.product-details-wrapper .product-info ul.ratings {
  margin-bottom: 10px;
}
.product-details-wrapper .product-info span.price {
  font: 600 20px "Prompt", sans-serif;
  margin-bottom: 20px;
}
.product-details-wrapper .product-info P {
  margin-bottom: 10px;
}
.product-details-wrapper .product-info .product-cart ul li {
  display: inline-block;
  margin-bottom: 10px;
}
.product-details-wrapper .product-info .product-cart ul li .quantity-input {
  -webkit-margin-end: 15px;
          margin-inline-end: 15px;
}
.product-details-wrapper .product-info .product-meta {
  border-bottom: 1px solid rgba(11, 61, 44, 0.1);
}
.product-details-wrapper .product-info .product-meta li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.product-details-wrapper .product-info .product-meta li span {
  font: 600 16px "Prompt", sans-serif;
  width: 30%;
}
.product-details-wrapper .product-info .product-meta li a {
  font-weight: 400;
  color: #484848;
}
.product-details-wrapper .product-info .product-meta li a:before {
  display: inline-block;
  content: ":";
  -webkit-margin-end: 10px;
          margin-inline-end: 10px;
  -webkit-margin-start: 10px;
          margin-inline-start: 10px;
}
.product-details-wrapper .product-info ul.social-link li span {
  font: 600 16px "Prompt", sans-serif;
  -webkit-margin-end: 25px;
          margin-inline-end: 25px;
}
.product-details-wrapper .product-info ul.social-link li a {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(247, 146, 30, 0.1);
  font-size: 14px;
  -webkit-margin-end: 5px;
          margin-inline-end: 5px;
}
/*--==== Description Tabs ====--*/
.description-tabs ul.nav .nav-item:first-child .nav-link {
  padding-left: 0px;
}
.description-tabs .nav-link {
  position: relative;
  padding: 0 30px 20px;
  font: 400 18px "Prompt", sans-serif;
  border-bottom: 2px solid transparent;
  margin-bottom: 20px;
  color: #484848;
}
@media (max-width: 1199px) {
  .description-tabs .nav-link {
    padding: 0 15px 20px;
    font-size: 16px;
  }
}
.description-tabs .nav-link:hover, .description-tabs .nav-link.active {
  border-color: #ff9d00;
}

/*--==== Description Wrapper ====--*/
.description-wrapper .content-box p {
  margin-bottom: 20px;
}
.description-wrapper .content-box ul.check-style-one li i {
  font-size: 16px;
}

/*--==== Review Area ====--*/
.review-form-area {
  padding: 55px 65px;
}
@media (max-width: 1199px) {
  .review-form-area {
    padding: 55px 40px;
  }
}
.review-form-area h3.title {
  font-size: 27px;
  margin-bottom: 5px;
}
@media (max-width: 767px) {
  .review-form-area h3.title {
    font-size: 22px;
  }
}
.review-form-area P {
  margin-bottom: 22px;
}
.review-form-area P img {
  -webkit-margin-start: 10px;
          margin-inline-start: 10px;
}

.review-form .form_control {
  padding: 20px 25px;
  margin-bottom: 20px;
  border: 1px solid rgba(11, 61, 44, 0.1);
  border-radius: 10px;
  line-height: 1.5;
}
.review-form .form_control.placeholder {
  color: #0B3D2C;
}
.review-form .form_control:-moz-placeholder {
  color: #0B3D2C;
}
.review-form .form_control::-moz-placeholder {
  color: #0B3D2C;
}
.review-form .form_control::-webkit-input-placeholder {
  color: #0B3D2C;
}

/*--==== Quantity-input ====--*/
.quantity-input {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.quantity-input button {
  width: 60px;
  height: 56px;
  background-color: transparent;
  border: 1px solid rgba(11, 61, 44, 0.1);
}
.quantity-input input {
  width: 60px;
  height: 56px;
  border: 1px solid rgba(11, 61, 44, 0.1);
  text-align: center;
}

/*---==================
    15. Blog css 
=================----*/
.cat-btn {
  font: 500 18px "Prompt", sans-serif;
  line-height: 28px;
  padding: 6px 20px;
}

.post-meta span {
  margin-bottom: 10px;
  font-weight: 400;
  text-transform: uppercase;
}
.post-meta span i {
  margin-right: 10px;
}

.single-blog-post .post-thumbnail img {
  width: 100%;
  border-radius: 15px;
}
.single-blog-post .entry-content {
  padding: 0 15px 0;
}
.single-blog-post .entry-content .cat-btn {
  -webkit-transform: rotate(-6.08deg) translate(20px, -25px);
  transform: rotate(-6.08deg) translate(20px, -25px);
}
.single-blog-post .entry-content .post-meta span {
  margin-bottom: 7px;
}
.single-blog-post .entry-content h3.title {
  margin-bottom: 30px;
}
.single-blog-post-two {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 767px) {
  .single-blog-post-two {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
}
.single-blog-post-two:not(:last-child) {
  padding-bottom: 40px;
  border-bottom: 1px solid rgba(29, 35, 31, 0.1);
}
.single-blog-post-two .post-thumbnail {
  width: 270px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  margin-right: 30px;
}
.single-blog-post-two .post-thumbnail img {
  width: 100%;
  border-radius: 7px;
}
@media (max-width: 767px) {
  .single-blog-post-two .post-thumbnail {
    margin-right: 0;
    margin-bottom: 30px;
  }
}
.single-blog-post-two .entry-content h3.title {
  margin-bottom: 30px;
}
.single-blog-post-three .post-thumbnail img {
  width: 100%;
  border-radius: 15px;
}
.single-blog-post-three .entry-content {
  padding: 30px 15px 0;
}
.single-blog-post-three .entry-content h3.title {
  font-weight: 500;
  margin-bottom: 23px;
  line-height: 31px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .single-blog-post-three .entry-content h3.title {
    font-size: 20px;
  }
}

.single-blog-post-four {

  padding: 15px;
  -webkit-box-shadow: 0px 7px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 7px 60px rgba(0, 0, 0, 0.07);
  border-radius: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 767px) {
  .single-blog-post-four {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
.single-blog-post-four .post-thumbnail {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 30% auto;
  width: 30%;
  
}
.single-blog-post-four .post-thumbnail img {
  width: 100%;
  border-radius: 20px;
}
@media (max-width: 767px) {
  .single-blog-post-four .post-thumbnail {
    width: 100%;
    margin-bottom: 30px;
  }
}
.single-blog-post-four .entry-content {
  padding-left: 50px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
      flex: 1; 
  width: 49%;
}
@media (max-width: 767px) {
  .single-blog-post-four .entry-content {
    width: 100%;
    padding-left: 0;
    padding: 15px;
  }
}
.single-blog-post-four .entry-content h3.title {
  font-size: 35px;
  line-height: 45.5px;
  margin-bottom: 19px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .single-blog-post-four .entry-content h3.title {
    font-size: 24px;
    line-height: 35px;
  }
}
@media (max-width: 1199px) {
  .single-blog-post-four .entry-content h3.title {
    font-size: 24px;
    line-height: 35px;
  }
}
@media (max-width: 767px) {
  .single-blog-post-four .entry-content h3.title {
    font-size: 20px;
    line-height: 30px;
  }
}
.single-blog-post-four .entry-content .post-meta span:hover a {
  color: #ff9d00;
}
.single-blog-post-four .entry-content h6.author {
  font: 400 18px "Prompt", sans-serif;
  color: #484848;
  margin-bottom: 33px;
}
.single-blog-post-four .entry-content h6.author i {
  margin-right: 10px;
}
.blog-post .post-thumbnail img {
  width: 100%;
  border-radius: 20px;
}
.blog-post .post-meta {
  border-bottom: 1px solid rgba(29, 35, 31, 0.1);
}
.blog-post .post-meta span {
  margin-left: 20px;
  margin-right: 20px;
  font: 400 16px "Prompt", sans-serif;
  text-transform: uppercase;
}
.blog-post .post-meta span:hover a {
  color: #ff9d00;
}
@media (max-width: 767px) {
  .blog-post .post-meta span {
    margin-left: 0;
  }
}
.blog-post .entry-content h3.title {
  font-size: 37px;
  line-height: 44px;
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .blog-post .entry-content h3.title {
    font-size: 24px;
    line-height: 35px;
  }
}
.blog-post .entry-content p {
  margin-bottom: 30px;
}
.blog-post .entry-content h4 {
  font-size: 27px;
  margin-bottom: 12px;
}
.blog-post .entry-content .block-quote {
  padding: 40px;
  border-radius: 15px;
  position: relative;
  padding: 30px 40px 35px 110px;
}
@media (max-width: 767px) {
  .blog-post .entry-content .block-quote {
    padding: 30px 30px 35px;
  }
}
.blog-post .entry-content .block-quote img {
  position: absolute;
  top: 37px;
  left: 40px;
}
@media (max-width: 767px) {
  .blog-post .entry-content .block-quote img {
    position: relative;
    top: auto;
    left: auto;
    margin-bottom: 20px;
  }
}
.blog-post .entry-content .block-quote h3 {
  font-size: 27px;
  margin-bottom: 10px;
}
@media (max-width: 767px) {
  .blog-post .entry-content .block-quote h3 {
    font-size: 20px;
  }
}
.blog-post .entry-content .block-quote span {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font: 4600 18px "Prompt", sans-serif;
}
.blog-post .entry-content .block-quote span:before {
  content: "";
  width: 73px;
  height: 2px;
  -webkit-margin-end: 15px;
          margin-inline-end: 15px;
}
.blog-post .entry-footer {
  padding-top: 40px;
  padding-bottom: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media (max-width: 1199px) {
  .blog-post .entry-footer {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
.blog-post .entry-footer .tag-links {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 575px) {
  .blog-post .entry-footer .tag-links {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
.blog-post .entry-footer .tag-links h6 {
  margin-right: 15px;
  font-size: 16px;
  margin-bottom: 10px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .blog-post .entry-footer .tag-links h6 {
    margin-right: 10px;
  }
}
.blog-post .entry-footer .tag-links a {
  padding: 7px 20px;
  font: 600 14px "Prompt", sans-serif;
  line-height: 1.2;
  border: 1px solid rgba(11, 61, 44, 0.1);
  border-radius: 5px;
  -webkit-margin-end: 10px;
          margin-inline-end: 10px;
  margin-bottom: 10px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .blog-post .entry-footer .tag-links a {
    padding: 7px 10px;
  }
}
.blog-post .entry-footer .tag-links a:hover {
  border-color: transparent;
}
.blog-post .entry-footer .social-share {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 1199px) {
  .blog-post .entry-footer .social-share {
    margin-top: 15px;
  }
}
.blog-post .entry-footer .social-share h6 {
  -webkit-margin-end: 15px;
          margin-inline-end: 15px;
  font-size: 16px;
  margin-bottom: 10px;
}
.blog-post .entry-footer .social-share a {
  -webkit-margin-end: 20px;
          margin-inline-end: 20px;
  margin-bottom: 10px;
}
/* Post Author Box */
.post-author-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-shadow: 0px 7px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 7px 60px rgba(0, 0, 0, 0.07);
  border-radius: 12px;
  padding: 40px;
}
@media (max-width: 575px) {
  .post-author-box {
    padding: 40px 30px;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
.post-author-box .author-thumb {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 190px;
  height: 160px;
  -webkit-margin-end: 50px;
          margin-inline-end: 50px;
}
.post-author-box .author-thumb img {
  border-radius: 7px;
}
@media (max-width: 575px) {
  .post-author-box .author-thumb {
    margin-bottom: 40px;
  }
}
.post-author-box .author-content span.position {
  margin-bottom: 5px;
}
.post-author-box .author-content p {
  margin-bottom: 5px;
}
.post-author-box .author-content ul.social-link li {
  -webkit-margin-end: 10px;
          margin-inline-end: 10px;
}
/* Post Navigation Item */
.post-navigation-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media (max-width: 1199px) {
  .post-navigation-item {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .post-navigation-item {
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
  }
}
.post-navigation-item .post-nav-item {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  max-width: 370px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .post-navigation-item .post-nav-item {
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .post-navigation-item .post-nav-item {
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
}
.post-navigation-item .post-nav-item .thumb {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 130px;
  height: 130px;
  margin-right: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .post-navigation-item .post-nav-item .thumb {
    width: 100px;
    height: 100px;
    margin-right: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .post-navigation-item .post-nav-item .thumb {
    width: 100px;
    height: 100px;
    margin-right: 20px;
  }
}
.post-navigation-item .post-nav-item .content h6 {
  font-size: 18px;
  padding-bottom: 15px;
  line-height: 1.2;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(28, 35, 31, 0.1);
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .post-navigation-item .post-nav-item .content h6 {
    font-size: 16px;
    line-height: 30px;
  }
}
@media (max-width: 991px) {
  .post-navigation-item .post-nav-item .content h6 {
    font-size: 16px;
    line-height: 30px;
  }
}
.post-navigation-item .post-nav-item .content span.post-date {
  line-height: 1;
}
.post-navigation-item .post-nav-item .content span.post-date a i {
  -webkit-margin-end: 7px;
          margin-inline-end: 7px;
}

/* Comments Area */
.comments-area h5.comments-title {
  padding-bottom: 25px;
  border-bottom: 1px solid rgba(28, 35, 31, 0.1);
}
.comments-area .comment {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 35px;
}
.comments-area .comment .comment-avatar {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 100px;
  height: 100px;
  margin-right: 40px;
}
@media (max-width: 1199px) {
  .comments-area .comment .comment-avatar {
    margin-right: 20px;
  }
}
@media (max-width: 575px) {
  .comments-area .comment .comment-avatar {
    margin-bottom: 20px;
  }
}
.comments-area .comment .comment-wrap {
  padding-bottom: 35px;
  border-bottom: 1px solid rgba(28, 35, 31, 0.1);
}
.comments-area .comment .comment-wrap .comment-author-content span.author-name {
  display: block;
  font: 500 22px "Prompt", sans-serif;
  margin-bottom: 2px;
}
.comments-area .comment .comment-wrap .comment-author-content span.author-name span.date {
  display: block;
  font: 300 16px "Prompt", sans-serif;
  margin-bottom: 6px;
}
.comments-area .comment .comment-wrap .comment-author-content span.author-name span.time {
  float: right;
  font: 300 16px "Prompt", sans-serif;
}
.comments-area .comment .comment-wrap .comment-author-content span.author-name span.time i {
  margin-right: 5px;
}
@media (max-width: 767px) {
  .comments-area .comment .comment-wrap .comment-author-content span.author-name span.time {
    float: left;
    margin-bottom: 10px;
  }
}
.comments-area .comment .comment-wrap .comment-author-content P {
  margin-bottom: 8px;
}
.comments-area .comment-reply .comment {
  margin-left: 70px;
}
@media (max-width: 575px) {
  .comments-area .comment-reply .comment {
    margin-left: 20px;
  }
}

.comment-rating-ul {
  margin-top: 15px;
}
.comment-rating-ul li {
  display: inline-block;
}
.comment-rating-ul li:not(:last-child) {
  margin-right: 30px;
}
.comment-rating-ul li span.title {
  color: #A5A5A5;
  font: 400 14px "Prompt", sans-serif;
  display: block;
  line-height: 20px;
}
.comment-rating-ul li span {
  line-height: 1;
}
.comment-rating-ul li span i {
  font-size: 10px;
}

/* Comments Respond */
.comments-respond {
  padding: 45px 45px 50px;
  border-radius: 12px;
}
@media (max-width: 767px) {
  .comments-respond {
    padding: 45px 30px 50px;
  }
}
.comments-respond h3.comments-heading {
  margin-bottom: 5px;
}
.comments-respond p {
  margin-bottom: 30px;
}

/* Comment Form */
form.comment-form .form_control {
  margin-bottom: 20px;
  background-color: #f0f9ff;
  border: 1px solid transparent;
  border-radius: 5px;
  font-weight: 400;
  padding: 17px 25px;
  line-height: 24px;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
form.comment-form .form_control.placeholder {
  color: #1C231F;
}
form.comment-form .form_control:-moz-placeholder {
  color: #1C231F;
}
form.comment-form .form_control::-moz-placeholder {
  color: #1C231F;
}
form.comment-form .form_control::-webkit-input-placeholder {
  color: #1C231F;
}
form.comment-form .form_control:focus {
  border-color: #1C231F;
}

/* Sidebar Widget Area */
.sidebar-widget-area .sidebar-widget {
  padding: 35px 40px 40px;
  border-radius: 7px;
  -webkit-box-shadow: 0px 7px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 7px 60px rgba(0, 0, 0, 0.07);
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .sidebar-widget-area .sidebar-widget {
    padding: 35px 20px 40px;
  }
}
@media (max-width: 575px) {
  .sidebar-widget-area .sidebar-widget {
    padding: 35px 20px 40px;
  }
}
.sidebar-widget-area .sidebar-widget h4.widget-title:after {
  display: block;
  content: "";
  margin-top: 20px;
  border-bottom: 1px solid rgba(29, 35, 31, 0.1);
  margin-bottom: 23px;
}
.sidebar-widget-area .search-widget {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.sidebar-widget-area .search-widget h4.widget-title {
  margin-bottom: 17px;
}
.sidebar-widget-area .search-widget h4.widget-title:after {
  display: none;
}
.sidebar-widget-area .search-widget form label {
  position: absolute;
  right: 25px;
  top: 22px;
}
.sidebar-widget-area .search-widget form .form_control {
  border-radius: 7px;
  -webkit-box-shadow: 0px 7px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 7px 60px rgba(0, 0, 0, 0.07);
  padding: 20px 25px;
  line-height: 25px;
}
.sidebar-widget-area .category-widget {
  padding: 35px 40px;
}
.sidebar-widget-area .category-widget h5.widget-title {
  margin-bottom: 24px;
}
.sidebar-widget-area .category-widget ul.category-nav li:not(:last-child) a {
  border-bottom: 1px solid rgba(28, 35, 31, 0.1);
  padding-bottom: 12px;
  margin-bottom: 22px;
}
.sidebar-widget-area .category-widget ul.category-nav li a {
  font: 500 18px "Prompt", sans-serif;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.sidebar-widget-area .category-widget ul.category-nav li a:hover {
  border-color: #ff9d00;
}
.sidebar-widget-area .category-widget ul.category-nav li a i {
  -webkit-margin-start: auto;
          margin-inline-start: auto;
}
.sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 575px) {
  .sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content {
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
}
.sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content:not(:last-child) {
  margin-bottom: 40px;
  border-bottom: 1px solid rgba(28, 35, 31, 0.1);
  padding-bottom: 40px;
}
.sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 130px;
          flex: 0 0 130px;
  max-width: 130px;
  height: 130px;
  border-radius: 7px;
  margin-right: 30px;
}
@media (max-width: 575px) {
  .sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content img {
    max-width: 80px;
    height: 80px;
    margin-right: 20px;
  }
}
.sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content .post-title-date span.posted-on {
  line-height: 130%;
  font-weight: 300;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content .post-title-date span.posted-on {
    font-size: 14px;
  }
}
@media (max-width: 575px) {
  .sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content .post-title-date span.posted-on {
    font-size: 14px;
  }
}
.sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content .post-title-date span.posted-on i {
  -webkit-margin-end: 10px;
          margin-inline-end: 10px;
}
.sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content .post-title-date h5 {
  font: 500 18px "Prompt", sans-serif;
  line-height: 120%;
  padding-bottom: 13px;
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(28, 35, 31, 0.1);
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content .post-title-date h5 {
    font-size: 16px;
  }
}
@media (max-width: 575px) {
  .sidebar-widget-area .recent-post-widget .recent-post-list .post-thumbnail-content .post-title-date h5 {
    font-size: 16px;
  }
}
.sidebar-widget-area .sidebar-banner-widget {
  padding: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.sidebar-widget-area .sidebar-banner-widget .banner-widget-content .banner-img {
  position: relative;
  border-radius: 7px;
  overflow: hidden;
}
.sidebar-widget-area .sidebar-banner-widget .banner-widget-content .banner-img img {
  width: 100%;
}
.sidebar-widget-area .sidebar-banner-widget .banner-widget-content .banner-img .hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(176.84deg, rgba(0, 0, 0, 0) 2.33%, rgba(0, 0, 0, 0.75) 97.38%);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  padding: 30px 35px 22px;
}
.sidebar-widget-area .sidebar-banner-widget .banner-widget-content .banner-img .hover-content h4.title {
  font-size: 27px;
  margin-bottom: 8px;
}
.sidebar-widget-area .sidebar-banner-widget .banner-widget-content .banner-img .hover-content p i {
  font-size: 20px;
  margin-right: 10px;
}
.sidebar-widget-area .tag-cloud-widget {
  padding: 35px 40px 30px;
}
.sidebar-widget-area .tag-cloud-widget h4.widget-title:after {
  margin-bottom: 28px;
}
.sidebar-widget-area .tag-cloud-widget a {
  padding: 10px 26px;
  font: 500 16px "Prompt", sans-serif;
  -webkit-box-shadow: 0px 7px 60px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 7px 60px rgba(0, 0, 0, 0.07);
  border-radius: 5px;
  margin-bottom: 10px;
  line-height: 19px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .sidebar-widget-area .tag-cloud-widget a {
    font-size: 14px;
  }
}
@media (max-width: 575px) {
  .sidebar-widget-area .tag-cloud-widget a {
    font-size: 14px;
  }
}
.sidebar-widget-area .widget-product-banner .banner-content {
  position: relative;
  padding: 35px 40px 40px;
  background-color: rgba(99, 171, 69, 0.1);
  border-radius: 10px;
  overflow: hidden;
}
.sidebar-widget-area .widget-product-banner .banner-content:after {
  position: absolute;
  bottom: 0;
  left: 0;
  content: "";
  width: 100%;
  height: 70%;
  -webkit-clip-path: polygon(0 40%, 100% 20%, 100% 100%, 0% 100%);
          clip-path: polygon(0 40%, 100% 20%, 100% 100%, 0% 100%);
  background-color: rgba(99, 171, 69, 0.1);
  z-index: -1;
}
.sidebar-widget-area .widget-product-banner .banner-content h4.title {
  margin-bottom: 25px;
}
.sidebar-widget-area .widget-product-banner .banner-content img {
  margin-bottom: 25px;
}
.sidebar-widget-area .booking-form-widget .sidebar-booking-form .booking-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(29, 35, 31, 0.1);
}
.sidebar-widget-area .booking-form-widget .sidebar-booking-form .booking-item label {
  font: 500 16px "Prompt", sans-serif;
}
.sidebar-widget-area .booking-form-widget .sidebar-booking-form .bk-item {
  position: relative;
}
.sidebar-widget-area .booking-form-widget .sidebar-booking-form .bk-item i {
  position: absolute;
  top: 12px;
  left: 10px;
  z-index: 2;
}
.sidebar-widget-area .booking-form-widget .sidebar-booking-form .bk-item .nice-select, .sidebar-widget-area .booking-form-widget .sidebar-booking-form .bk-item input {
  width: 150px;
  line-height: 20px;
  padding: 10px 10px 10px 35px;
  border: 1px solid rgba(29, 35, 31, 0.2);
  border-radius: 7px;
}
.sidebar-widget-area .booking-form-widget .sidebar-booking-form .bk-item .nice-select:after {
  right: 10px;
}
.sidebar-widget-area .booking-form-widget .sidebar-booking-form .bk-item.booking-user .nice-select {
  width: 100px;
}
.sidebar-widget-area .booking-form-widget .sidebar-booking-form .booking-extra .extra {
  padding: 10px 0;
  border-bottom: 1px solid rgba(29, 35, 31, 0.2);
  font: 500 16px "Prompt", sans-serif;
}
.sidebar-widget-area .booking-form-widget .sidebar-booking-form .booking-extra .extra i {
  margin-right: 10px;
}
.sidebar-widget-area .booking-form-widget .sidebar-booking-form .booking-extra .extra span {
  float: right;
}
.sidebar-widget-area .booking-form-widget .sidebar-booking-form .booking-total .total {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  font: 500 16px "Prompt", sans-serif;
}
.sidebar-widget-area .booking-form-widget .sidebar-booking-form .submit-button .main-btn {
  width: 100%;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.sidebar-widget-area .booking-info-widget {
  padding: 35px 40px 30px;
}
.sidebar-widget-area .booking-info-widget ul.info-list li:not(:last-child) {
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(29, 35, 31, 0.1);
}
.sidebar-widget-area .booking-info-widget ul.info-list li span {
  font: 300 18px "Prompt", sans-serif;
  display: block;
}
.sidebar-widget-area .booking-info-widget ul.info-list li span i {
  margin-right: 10px;
}
.sidebar-widget-area .booking-info-widget ul.info-list li span span {
  font-weight: 500;
  float: right;
}
.sidebar-widget-area .recent-place-widget .recent-place-list .place-thumbnail-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.sidebar-widget-area .recent-place-widget .recent-place-list .place-thumbnail-content:not(:last-child) {
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(29, 35, 31, 0.1);
  margin-bottom: 30px;
}
.sidebar-widget-area .recent-place-widget .recent-place-list .place-thumbnail-content img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 100px;
  border-radius: 7px;
  margin-right: 25px;
}
.sidebar-widget-area .recent-place-widget .recent-place-list .place-thumbnail-content .place-content ul.ratings li {
  font-size: 12px;
}
.sidebar-widget-area .recent-place-widget .recent-place-list .place-thumbnail-content .place-content span.price {
  font: 500 14px "Prompt", sans-serif;
}
.sidebar-widget-area .recent-place-widget .recent-place-list .place-thumbnail-content .place-content span.price .text {
  font: 300 14px "Prompt", sans-serif;
  margin-right: 10px;
  color: #868686;
}

/*---==================
    16. Contact css 
=================----*/
/* Contact Info Item */
.contact-info-item {
  border-radius: 12px;
  padding: 60px 70px 55px;
}
@media (max-width: 1199px) {
  .contact-info-item {
    padding: 60px 30px 55px;
  }
}
.contact-info-item .icon {
  margin-bottom: 32px;
  width: 100px;
  height: 100px;
  display: inline-block;
}
.contact-info-item .info span.title {
  font-size: 20px;
  line-height: 32px;
  margin-bottom: 16px;
}
.contact-info-item .info p {
  font-size: 22px;
  line-height: 30.8px;
}
@media (max-width: 1199px) {
  .contact-info-item .info p {
    font-size: 18px;
  }
}

/* Contact Form */
.contact-form .form_control {
  border: 1px solid rgba(29, 35, 31, 0.1);
  border-radius: 5px;
  margin-bottom: 30px;
  padding: 20px 35px;
  line-height: 28px;
  font-size: 18px;
}
/* Contact Page Map */
.contact-page-map .map-box iframe {
  height: 785px;
}
@media (max-width: 767px) {
  .contact-page-map .map-box iframe {
    height: 585px;
  }
}

/*---==================
    17. Footer css 
=================----*/
.black-bg .footer-copyright, .black-bg .footer-widget {
  color: #E1E1E1;
}
.black-bg .footer-top .social-box ul.social-link li a {
  background-color: rgba(255, 255, 255, 0.1);
  color: #B1B6B3;
}
.black-bg .single-info-item .info p {
  color: #E1E1E1;
}
.black-bg .footer-cta {
  border-color: rgba(217, 217, 217, 0.1);
}
.black-bg .footer-top, .black-bg .footer-copyright {
  border-color: rgba(217, 217, 217, 0.1);
}

.footer-top {
  border-top: 1px solid rgba(29, 35, 31, 0.1);
  border-bottom: 1px solid rgba(29, 35, 31, 0.1);
}
.footer-top .social-box ul.social-link li:not(:last-child) {
  margin-left: 5px;
}
.footer-top .social-box ul.social-link li a {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background-color: rgba(29, 35, 31, 0.1);
}
.footer-cta {
  border-bottom: 1px solid rgba(29, 35, 31, 0.1);
}

@media (max-width: 991px) {
  .footer-widget {
    font-size: 14px;
  }
}
.footer-widget h4.widget-title {
  margin-bottom: 20px;
}
.footer-widget .footer-content p {
  margin-bottom: 30px;
}

.service-nav-widget .footer-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 20px;
}
.service-nav-widget .footer-content .footer-widget-nav {
  width: 45%;
}
.service-nav-widget .footer-content .footer-widget-nav li:not(:last-child) {
  margin-bottom: 10px;
}
.footer-newsletter-widget .footer-content form .form_control {
  padding: 15px 20px 15px 25px;
  border-radius: 7px;
  font-weight: 400;
  -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.1);
}
.footer-newsletter-widget .footer-content form .form_control.placeholder {
  color: #1C231F;
}
.footer-newsletter-widget .footer-content form .form_control:-moz-placeholder {
  color: #1C231F;
}
.footer-newsletter-widget .footer-content form .form_control::-moz-placeholder {
  color: #1C231F;
}
.footer-newsletter-widget .footer-content form .form_control::-webkit-input-placeholder {
  color: #1C231F;
}
.footer-newsletter-widget .footer-content form label {
  position: absolute;
  top: 15px;
  right: 20px;
}

.footer-copyright {
  padding: 20px 0;
  border-top: 1px solid rgba(29, 35, 31, 0.1);
}
@media (max-width: 991px) {
  .footer-copyright .footer-text {
    text-align: center;
    margin-bottom: 15px;
  }
}
@media (max-width: 991px) {
  .footer-copyright .footer-nav {
    text-align: center;
  }
}
.footer-copyright .footer-nav ul li {
  display: inline-block;
}
.footer-copyright .footer-nav ul li:not(:last-child) {
  margin-right: 35px;
}
@media (max-width: 991px) {
  .footer-copyright .footer-nav ul li:not(:last-child) {
    margin-left: 10px;
    margin-right: 10px;
  }
}
/*---==================
    18. Sliders css 
=================----*/
/* Slider Dots */
ul.slick-dots li {
  position: relative;
  width: 20px;
  height: 20px;
  border: 1px solid transparent;
  border-radius: 50%;
}
ul.slick-dots li:after {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  content: "";
  width: 7px;
  height: 7px;
  border-radius: 50%;
}
ul.slick-dots li.slick-active {
  border-color: #ff9d00;
}

/* Hero Slider One */
.hero-slider-one .slick-arrow {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  cursor: pointer;
  z-index: 1;
  width: 65px;
  height: 65px;
  border-radius: 50%;
  font-size: 18px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.hero-slider-one .slick-arrow.prev {
  left: 70px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .hero-slider-one .slick-arrow.prev {
    left: 30px;
  }
}
.hero-slider-one .slick-arrow.next {
  right: 70px;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .hero-slider-one .slick-arrow.next {
    right: 30px;
  }
}
.hero-slider-one .slick-arrow:hover {
  border-color: transparent;
}

/* Hero Slider Two */
.hero-slider-two .slick-arrow {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  cursor: pointer;
  z-index: 1;
  width: 65px;
  height: 65px;
  border-radius: 50%;
  font-size: 18px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.hero-slider-two .slick-arrow.prev {
  left: 40px;
}
.hero-slider-two .slick-arrow.next {
  right: 40px;
}
.hero-slider-two .slick-arrow:hover {
  border-color: transparent;
}

.slider-active-4-item,
.slider-active-5-item,
.slider-active-3-item-dot,
.slider-active-3-item {
  margin-left: -15px;
  margin-right: -15px;
}
@media (max-width: 767px) {
  .slider-active-4-item,
.slider-active-5-item,
.slider-active-3-item-dot,
.slider-active-3-item {
    margin-left: -12px;
    margin-right: -12px;
  }
}
.slider-active-4-item .slick-slide,
.slider-active-5-item .slick-slide,
.slider-active-3-item-dot .slick-slide,
.slider-active-3-item .slick-slide {
  margin-left: 15px;
  margin-right: 15px;
}
@media (max-width: 767px) {
  .slider-active-4-item .slick-slide,
.slider-active-5-item .slick-slide,
.slider-active-3-item-dot .slick-slide,
.slider-active-3-item .slick-slide {
    margin-left: 12px;
    margin-right: 12px;
  }
}

.slider-active-3-item-dot .slick-dots {
  margin-top: 45px;
  text-align: center;
}

.recent-place-slider {
  margin-left: -15px;
  margin-right: -15px;
}
@media (max-width: 767px) {
  .recent-place-slider {
    margin-left: -12px;
    margin-right: -12px;
  }
}
.recent-place-slider .slick-slide {
  margin-left: 15px;
  margin-right: 15px;
}
@media (max-width: 767px) {
  .recent-place-slider .slick-slide {
    margin-left: 12px;
    margin-right: 12px;
  }
}

/*--==== Partners Css ====--*/
.partner-slider-one {
  border-top: 1px solid rgba(28, 35, 31, 0.1);
  margin-left: -15px;
  margin-right: -15px;
}
@media (max-width: 575px) {
  .partner-slider-one {
    margin-left: -12px;
    margin-right: -12px;
  }
}
.partner-slider-one .slick-slide {
  margin-left: 15px;
  margin-right: 15px;
}
@media (max-width: 575px) {
  .partner-slider-one .slick-slide {
    margin-left: -12px;
    margin-right: -12px;
  }
}

/*---==================
    19. Wrapper css 
=================----*/
.overlay {
  position: relative;
  z-index: 1;
}
.overlay:after {
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: -1;
}

.black-bg .sub-title {
  background-color: rgba(247, 146, 30, 0.1);
}

.skill-wrapper {
  padding: 40px 45px 34px;
  border-radius: 10px;
  -webkit-box-shadow: 0px 10px 60px rgba(28, 35, 31, 0.07);
          box-shadow: 0px 10px 60px rgba(28, 35, 31, 0.07);
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
@media (max-width: 575px) {
  .skill-wrapper {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
@media (max-width: 1199px) {
  .skill-wrapper {
    padding: 40px 30px 34px;
  }
}

.faq-wrapper {
  position: relative;
  margin-top: -300px;
}

.fun-wrapper {
  position: relative;
  margin-top: -160px;
  border-radius: 30px;
}

.reviews-wrapper {
  border: 1px solid rgba(29, 35, 31, 0.1);
  border-radius: 7px;
}
.reviews-wrapper .reviews-inner-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 767px) {
  .reviews-wrapper .reviews-inner-box {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
.reviews-wrapper .reviews-inner-box .rating-value {
  text-align: center;
  width: 40%;
  padding: 30px 40px 35px;
  border: 1px solid rgba(29, 35, 31, 0.1);
}
@media (max-width: 767px) {
  .reviews-wrapper .reviews-inner-box .rating-value {
    width: 100%;
  }
}
.reviews-wrapper .reviews-inner-box .rating-value .rate-score {
  font: 500 130px "Prompt", sans-serif;
  line-height: 1;
}
.reviews-wrapper .reviews-inner-box .rating-value .ratings {
  margin-bottom: 20px;
}
.reviews-wrapper .reviews-inner-box .rating-value span.reviews {
  padding: 7px 20px;
  border-radius: 7px;
  font-weight: 500;
  line-height: 23px;
}
.reviews-wrapper .reviews-inner-box .reviews-progress {
  padding: 20px 60px;
  width: 60%;
}
@media (max-width: 767px) {
  .reviews-wrapper .reviews-inner-box .reviews-progress {
    width: 100%;
  }
}
.reviews-wrapper .reviews-inner-box .reviews-progress .single-progress-bar:not(:last-child) {
  margin-bottom: 38px;
}
/*# sourceMappingURL=style.css.map */