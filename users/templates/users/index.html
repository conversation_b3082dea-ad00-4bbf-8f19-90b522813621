{% extends 'users/basemain.html' %}
{% load static  %}


{% block content %}



   
    <section class="services-section black-bg pt-100  "style="background-color: #f8f3fc;">
        <div class="container">
          
              
                 
                    
            <style>
                /* Custom Font Declarations with Performance Optimization */
                @font-face {
                    font-family: 'Fonarto';
                    font-style: normal;
                    font-weight: 400;
                    src: local('Fonarto Regular'),
                         local('FonartoRegular-8Mon2'),
                         url('{% static "fonts/FonartoRegular-8Mon2.woff" %}') format('woff');
                    font-display: swap;
                }

                @font-face {
                    font-family: 'Fonarto';
                    font-style: normal;
                    font-weight: 300;
                    src: local('Fonarto Light'),
                         local('FonartoLight-BWxv3'),
                         url('{% static "fonts/FonartoLight-BWxv3.woff" %}') format('woff');
                    font-display: swap;
                }

                @font-face {
                    font-family: 'Fonarto';
                    font-style: normal;
                    font-weight: 700;
                    src: local('Fonarto Bold'),
                         local('FonartoBold-RpYOo'),
                         url('{% static "fonts/FonartoBold-RpYOo.woff" %}') format('woff');
                    font-display: swap;
                }

                /* H1 Custom Font Styling with Enhanced Typography */
                h1 {
                    font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                    font-weight: 700 !important;
                    letter-spacing: -0.02em;
                    line-height: 1.2;
                    text-rendering: optimizeLegibility;
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                    font-feature-settings: 'kern' 1;
                }

                .section-header h1 {
                    font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                    font-weight: 500 !important;
                    text-rendering: optimizeLegibility;
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                }

                /* Ensure font loading doesn't cause layout shift */
                .section-header h1,
                h1 {
                    font-synthesis: none;
                    font-variant-ligatures: common-ligatures;
                }

                /* Enhanced H1 styling for better visual impact */
                h1.display-4 {
                    font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                    font-weight: 700 !important;
                    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                /* Ensure consistent styling across all H1 elements */
                .section-title h1 {
                    font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                    font-weight: 700 !important;
                    text-rendering: optimizeLegibility;
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                }

               .check-list {
                    list-style: none;
                    padding: 0;
                }
                
                .check-list li {
                    margin-bottom: 1rem;
                    display: flex;
                    align-items: center;
                    color: #ffffff !important; /* Force white color */
                }

                .check-list li i {
                    color: #4CAF50;
                    margin-right: 0.75rem;
                    font-size: 1.25rem;
                }
                .single-place-item-two {
                    transition: all 0.3s ease-in-out;
                    overflow: hidden;
                    border-radius: 24px;
                }
            
                .single-place-item-two:hover {
                    /* transform: translateY(-5px); */
                    transform: translateY(-8px) scale(1.01);
                    box-shadow: 0 10px 20px rgba(6, 0, 43, 0.2);
                    border-radius: 24px;
                }
            
                .single-place-item-two .place-img {
                    position: relative;
                    overflow: hidden;
                    border-radius: 24x;
                }
            
                .single-place-item-two .place-img img {
                    /* transition: all 0.5s ease-in-out; */
                    transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 0.4);
                    border-radius: 24px;
                    object-fit: cover;
                }
            
                /* .single-place-item-two:hover .place-img img {
                    transform: scale(1.1);
                } */
                .single-place-item-two:hover .place-img img {
                transform: scale(1.08);
            }
            
                .single-place-item-two .place-content {
                    transition: all 0.3s ease-in-out;
                    border-radius: 20px;
                }
            
                .single-place-item-two:hover .place-content {
                    background: rgba(3, 0, 160 0.4);
                    border-radius: 20px;
                }
            
                .single-place-item-two a {
                    text-decoration: none;
                    display: block;
                    border-radius: 20px;
                }
                .nav-tabs {
                border: none;
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 0.5rem;
            }

            .nav-tabs .nav-link {
                border-radius: 50px;
                padding: 0.75rem 1.5rem;
                color: #0f238d;
                transition: all 0.3s ease;
                border: 2px solid transparent;
                background: rgba(255,255,255,0.9);
            }

            .nav-tabs .nav-link:hover {
                background: rgba(23, 11, 44, 0.1);
            }

            .nav-tabs .nav-link.active {
                background: #0f238d;
                color: white;
                border-color: #4CAF50;
            }

            .btn-custom {
                background: linear-gradient(135deg, #0f238d 0%, #2a1b3d 100%);
                color: white;
                border-radius: 50px;
                padding: 1rem 2rem;
                transition: all 0.3s ease;
                border: none;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }

            .btn-custom:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 12px rgba(0,0,0,0.15);
            }
            </style>
            
            <div class="row">
                <div class="col-lg-6 col-md-12">
                    <div class="single-place-item-two mb-30 wow fadeInUp">
                        <a href="{% url 'users:corporatepage' %}">
                            <div class="place-img">
                                <img src="{% static 'assets/images/place/place-12.jpg' %}" alt="Place Image">
                                <span class="tour-count">Business Travel</span>
                                <div class="place-content">
                                    <div class="info text-white">
                                        <h3 class="title mb-10"><strong>Efficient Corporate  <br> Travel Solutions</strong> </h3>
                                        <div data-animation="fadeInRight" data-delay=".6s"> 
                                            <span class="sub-title"> <small> For Corporate Travellers <i class="fas fa-paper-plane"></i></small> </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                
                <div class="col-lg-6 col-md-12">
                    <div class="single-place-item-two mb-30 wow fadeInUp">
                        <a href="{% url 'users:all_packages' %}">
                            <div class="place-img">
                                <img src="{% static 'assets/images/place/place-122.jpg' %}" alt="Place Image">
                                <span class="tour-count">20+ Destinations</span>
                                <div class="place-content">
                                    <div class="info text-white">
                                        <h3 class="title mb-10"><strong>Holiday Getaways<br> Great destinations, easy planning.</strong> </h3>
                                        <div data-animation="fadeInRight" data-delay=".6s"> 
                                            <span class="sub-title"> <small> Explore Holidays & Tours <i class="fas fa-paper-plane"></i></small> </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
         
       
      
       
    </section><!--====== End Services Section ======-->
<section class="expertise-features-section py-5" style="background-color: #f8f3fc;">
    <div class="container">
        <!-- Header Content -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-10 text-center">
                <div class="section-header wow fadeInUp">
                    <h1 class="display-4 fw-bold mb-4" style="color: #0f238d;">
                     Our mission is simple: to make your Travels Affordable, Memorable, and Stress-free.
                    </h1>
                    <p class="lead mb-0" style="color: #333; ; margin: 0 auto;">
                        <strong>Novustell Travel is a travel consultancy with over 20 years of experience, aiming to provide comprehensive and cost-effective travel solutions.</strong>
                     
                     We offer a wide range of services including corporate travel, MICE (Meetings, Incentives, Conferences, and Exhibitions), student travel, NGO travel, holiday packages, accommodation, car/van rentals, and corporate gifting.
                    </p>
                </div>
            </div>
        </div>

      
    </div>

    <!-- Enhanced CSS Styles -->
    <style>
        .expertise-features-section {
            position: relative;
            overflow: hidden;
        }

        .section-header {
            position: relative;
            z-index: 2;
        }

        .features-container {
            position: relative;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .features-container:hover {
            transform: translateY(-5px);
        }

        .contact-link {
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            padding: 8px 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
        }

        .contact-link:hover {
            color: #ff9d00;
            transform: translateX(5px);
            background: rgba(255, 157, 0, 0.1);
            border-color: #ff9d00;
        }

        .contact-link:hover i {
            animation: fly 0.6s ease-in-out;
        }

        @keyframes fly {
            0% { transform: translateX(0); }
            50% { transform: translateX(10px); }
            100% { transform: translateX(0); }
        }

        .check-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .check-list li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1.5rem;
            color: #ffffff !important;
            transition: transform 0.2s ease;
        }

        .check-list li:hover {
            transform: translateX(5px);
        }

        .check-list li i {
            color: #ff9d00;
            margin-right: 1rem;
            font-size: 1.1rem;
            margin-top: 0.25rem;
            transition: color 0.3s ease;
        }

        .check-list li:hover i {
            color: #ffb84d;
        }

        .check-list li div {
            flex: 1;
        }

        .check-list li strong {
            display: block;
            margin-bottom: 0.3rem;
            font-size: 1.05rem;
            font-weight: 600;
        }

        .check-list li p {
            color: rgba(255, 255, 255, 0.85) !important;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .section-content {
            position: relative;
        }

        .section-title h2 {
            font-size: 2.2rem;
            font-weight: 700;
            line-height: 1.3;
        }

        @media (max-width: 991px) {
            .section-content {
                margin-top: 3rem;
            }

            .features-container {
                padding: 40px 30px;
                margin-top: 30px;
            }

            .section-title h2 {
                font-size: 1.8rem;
            }

            .place-img img {
                height: 350px !important;
            }

            /* H1 responsive styling for tablets */
            h1 {
                font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                font-weight: 700 !important;
            }
        }

        @media (max-width: 767px) {
            .features-container {
                padding: 30px 20px;
            }

            .section-header h1 {
                font-size: 2rem;
                font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                font-weight: 700 !important;
                line-height: 1.1;
            }

            .section-title h2 {
                font-size: 1.6rem;
            }

            /* H1 responsive styling for mobile */
            h1 {
                font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                font-weight: 700 !important;
                letter-spacing: -0.01em;
            }
        }

        /* Font Loading Optimization */
        .font-loading h1 {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .font-loaded h1 {
            opacity: 1;
        }

        /* Ensure graceful fallback */
        .no-js h1 {
            opacity: 1;
        }
    </style>

    <!-- Font Loading Script -->
    <script>
        // Remove no-js class if JavaScript is enabled
        document.documentElement.classList.remove('no-js');
        document.documentElement.classList.add('font-loading');

        // Check if fonts are loaded
        if ('fonts' in document) {
            Promise.all([
                document.fonts.load('700 1em Fonarto'),
                document.fonts.load('400 1em Fonarto'),
                document.fonts.load('300 1em Fonarto')
            ]).then(function() {
                document.documentElement.classList.remove('font-loading');
                document.documentElement.classList.add('font-loaded');
            }).catch(function() {
                // Fallback if fonts fail to load
                document.documentElement.classList.remove('font-loading');
                document.documentElement.classList.add('font-loaded');
            });
        } else {
            // Fallback for browsers that don't support Font Loading API
            setTimeout(function() {
                document.documentElement.classList.remove('font-loading');
                document.documentElement.classList.add('font-loaded');
            }, 2000);
        }
    </script>
</section>
<section class="why-choose-section pt-50 pb-50" style="
    background-image: linear-gradient(rgba(15, 35, 141, 0.7), rgba(15, 35, 141, 0.7)), url('{% static "assets/images/place/place-12.jpg" %}');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
">
                <div class="container">
                    <div class="row align-items-xl-center">

                        <div class="col-xl-12">
                            <div class="choose-content-box pr-lg-70">
                                <!-- Section Title -->
                                <div class="section-title mb-45 wow fadeInDown ">
                                   
                                        <h1 class="display-4 fw-bold mb-4" style="color: #ffffff; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                                            Our Travel Solutions
                                        </h1>
                                </div>
                      





                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="fancy-icon-box-four mb-45 wow fadeInUp">
                                       
                                            <div class="text">
                                                <h4 class="title"><strong>MICE (Meetings, Incentives, Conferences & Exhibitions)</strong></h4>
                                                <p>Flawlessly planned events, conferences, and corporate gatherings for impactful experiences.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Expert Travel Consultants -->
                                    <div class="col-md-4">
                                        <a href="#" class="text-decoration-none">
                                            <div class="fancy-icon-box-four mb-45 wow fadeInUp position-relative">
                                                <div class="text">
                                                    <h4 class="title"> <strong>Corporate Travel</strong> </h4>
                                                    <p>Seamless business trips with tailored solutions for executives, teams, and professionals.</p>
                                                </div>
                                                <div class="view-more"></div>
                                            </div>
                                        </a>
                                    </div>
            
                                    <!-- Best Price Guarantee -->
                               
            
                                    <!-- 24/7 Customer Support -->
                                    <div class="col-md-4">
                                        <div class="fancy-icon-box-four mb-45 wow fadeInUp">
                                         
                                            <div class="text">
                                                <h4 class="title"><strong>Student Travel</strong></h4>
                                                <p>Educational and adventure-packed trips designed for students and academic groups.</p>
                                            </div>
                                        </div>
                                    </div>
            
                                    <!-- Tailored Travel Solutions -->
                                    <div class="col-md-4">
                                        <div class="fancy-icon-box-four mb-45 wow fadeInUp">
                                         
                                            <div class="text">
                                                <h4 class="title"><strong>NGO Travel</strong></h4>
                                                <p>Reliable travel services for humanitarian missions, volunteer groups, and field projects.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="fancy-icon-box-four mb-45 wow fadeInUp">
                                          
                                            <div class="text">
                                                <h4 class="title"><strong>Group Travel</strong></h4>
                                                <p>Hassle-free planning for friends, families, and organizations traveling together.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="fancy-icon-box-four mb-45 wow fadeInUp">
                                          
                                            <div class="text">
                                                <h4 class="title"><strong>Holiday Packages</strong></h4>
                                                <p>Curated getaways for relaxation, adventure, and unforgettable experiences worldwide.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center mt-4">
                                    <a href="{% url 'users:all_packages' %}" class="custom-button">
                                        Explore All Services <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>

                    
                      
                      
                    </div>
                </div>
            
                <!-- Add this CSS to your existing styles -->
                <style>
                    .custom-button {
                                        display: inline-block;
                                        padding: 15px 35px;
                                        color: #ffffff;
                                        background: rgba(255, 157, 0, 0.9);
                                        border: 2px solid #ff9d00;
                                        border-radius: 50px;
                                        text-decoration: none;
                                        transition: all 0.3s ease;
                                        font-weight: 600;
                                        backdrop-filter: blur(10px);
                                        box-shadow: 0 4px 15px rgba(255, 157, 0, 0.3);
                                    }

                                    .custom-button:hover {
                                        background: #ffffff;
                                        transform: translateY(-3px);
                                        box-shadow: 0 8px 25px rgba(255, 157, 0, 0.4);
                                        color: #0f238d;
                                        border-color: #ff9d00;
                                    }

                                    .custom-button i {
                                        margin-left: 8px;
                                        transition: transform 0.3s ease;
                                    }

                                    .custom-button:hover i {
                                        transform: translateX(5px);
                                        color: #0f238d;
                                    }
                    
                    .contact-link {
        color: #ff9d00;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-block;
    }

    .contact-link:hover {
        color: #ff9d00;
        transform: translateX(5px);
    }

    .contact-link:hover i {
        animation: fly 0.6s ease-in-out;
    }

    @keyframes fly {
        0% { transform: translateX(0); }
        50% { transform: translateX(10px); }
        100% { transform: translateX(0); }
    }
                    .fancy-icon-box-four {
                        display: flex;
                        align-items: flex-start;
                        padding: 25px;
                        background: rgba(255, 255, 255, 0.95);
                        border-radius: 15px;
                        transition: all 0.3s ease;
                        border: 2px solid #ff9d00;
                        position: relative;
                        overflow: hidden;
                        backdrop-filter: blur(10px);
                        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                    }
                    .view-more {
                        position: absolute;
                        bottom: -40px;
                        left: 50%;
                        transform: translateX(-50%);
                        color: white;
                        padding: 3px 10px;
                        border-radius: 20px;
                        transition: all 0.3s ease;
                        opacity: 0;
                    }

                    .fancy-icon-box-four:hover .view-more {
                        bottom: 15px;
                        opacity: 1;
                    }

            
            
                    .fancy-icon-box-four:hover {
                        transform: translateY(-8px);
                        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
                        background: rgba(255, 255, 255, 1);
                        border-color: #ff9d00;
                    }
            
                    .fancy-icon-box-four .icon {
                        font-size: 2rem;
                        color: #0f238d;
                        margin-right: 20px;
                        min-width: 60px;
                        text-align: center;
                    }
            
                    .fancy-icon-box-four .text {
                        flex: 1;
                    }
            
                    .fancy-icon-box-four .title {
                        font-size: 1.3rem;
                        margin-bottom: 10px;
                        color: #0f238d;
                    }
            
                    .fancy-icon-box-four p {
                        margin: 0;
                        color: #666;
                    }
            
                    .section-title .sub-title {
                        color: #4CAF50;
                        font-size: 1.4rem;
                        font-weight: 600;
                        margin-bottom: 10px;
                        display: block;
                    }
            
                    .gray-bg {
                        background-color: #f8f9fa;
                    }

                    /* Enhanced styling for the background image section */
                    .why-choose-section {
                        position: relative;
                        overflow: hidden;
                    }

                    .why-choose-section::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(15, 35, 141, 0.1);
                        z-index: 1;
                        pointer-events: none;
                    }

                    .why-choose-section .container {
                        position: relative;
                        z-index: 2;
                    }

                    /* Responsive background adjustments */
                    @media (max-width: 768px) {
                        .why-choose-section {
                            background-attachment: scroll;
                        }

                        .fancy-icon-box-four {
                            background: rgba(255, 255, 255, 0.98);
                            margin-bottom: 20px;
                        }
                    }

                    @media (max-width: 576px) {
                        .why-choose-section {
                            padding: 40px 0;
                        }

                        .custom-button {
                            padding: 12px 25px;
                            font-size: 0.9rem;
                        }
                    }
                </style>
            </section>
    
    
    <!--====== Start Featured Destinations Section ======-->
    <section class="featured-destinations-section py-5" style="background-color: #f8f3fc;">
        <div class="container">
            <!-- Section Header -->
            <div class="row justify-content-center mb-5">
                <div class="col-lg-8 text-center">
                    <div class="section-header wow fadeInUp">
                        <h1 class="display-4 fw-bold mb-4" style="color: #170b2c; font-family: 'Fonarto', sans-serif;">
                            Featured Destinations
                        </h1>
                        <p class="lead mb-0" style="color: #333;">
                            Discover breathtaking destinations around the world with our carefully curated travel packages.
                            From exotic beaches to cultural landmarks, we bring you the best travel experiences.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Destinations Slider -->
            <div class="destinations-slider-wrapper position-relative wow fadeInUp" data-wow-delay="0.1s" role="region" aria-label="Featured Destinations Carousel">
                <div class="destinations-slider" role="group" aria-label="Destination cards">
                    <!-- Destination 1: Maldives -->
                    <div class="destination-slide">
                        <div class="destination-card">
                            <div class="destination-image">
                                <img src="{% static 'assets/images/place/beach.jpg' %}" alt="Maldives" class="img-fluid">
                                <div class="destination-overlay">
                                    <div class="destination-content">
                                        <h4 class="destination-title">Maldives</h4>
                                        <p class="destination-description">Paradise islands with crystal-clear waters and luxury resorts</p>
                                        <div class="destination-price">Starting from $1,200</div>
                                        <a href="{% url 'users:all_packages' %}" class="destination-btn">
                                            <i class="fas fa-arrow-right"></i> Explore
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Destination 2: Dubai -->
                    <div class="destination-slide">
                        <div class="destination-card">
                            <div class="destination-image">
                                <img src="{% static 'assets/images/place/place-1.jpg' %}" alt="Dubai" class="img-fluid">
                                <div class="destination-overlay">
                                    <div class="destination-content">
                                        <h4 class="destination-title">Dubai</h4>
                                        <p class="destination-description">Modern marvels, luxury shopping, and desert adventures</p>
                                        <div class="destination-price">Starting from $800</div>
                                        <a href="{% url 'users:all_packages' %}" class="destination-btn">
                                            <i class="fas fa-arrow-right"></i> Explore
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Destination 3: Zanzibar -->
                    <div class="destination-slide">
                        <div class="destination-card">
                            <div class="destination-image">
                                <img src="{% static 'assets/images/place/place-2.jpg' %}" alt="Zanzibar" class="img-fluid">
                                <div class="destination-overlay">
                                    <div class="destination-content">
                                        <h4 class="destination-title">Zanzibar</h4>
                                        <p class="destination-description">Spice islands with pristine beaches and rich culture</p>
                                        <div class="destination-price">Starting from $600</div>
                                        <a href="{% url 'users:all_packages' %}" class="destination-btn">
                                            <i class="fas fa-arrow-right"></i> Explore
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Destination 4: Turkey -->
                    <div class="destination-slide">
                        <div class="destination-card">
                            <div class="destination-image">
                                <img src="{% static 'assets/images/place/place-3.jpg' %}" alt="Turkey" class="img-fluid">
                                <div class="destination-overlay">
                                    <div class="destination-content">
                                        <h4 class="destination-title">Turkey</h4>
                                        <p class="destination-description">Historic wonders bridging Europe and Asia</p>
                                        <div class="destination-price">Starting from $700</div>
                                        <a href="{% url 'users:all_packages' %}" class="destination-btn">
                                            <i class="fas fa-arrow-right"></i> Explore
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Destination 5: South Africa -->
                    <div class="destination-slide">
                        <div class="destination-card">
                            <div class="destination-image">
                                <img src="{% static 'assets/images/place/place-4.jpg' %}" alt="South Africa" class="img-fluid">
                                <div class="destination-overlay">
                                    <div class="destination-content">
                                        <h4 class="destination-title">South Africa</h4>
                                        <p class="destination-description">Wildlife safaris and stunning landscapes</p>
                                        <div class="destination-price">Starting from $900</div>
                                        <a href="{% url 'users:all_packages' %}" class="destination-btn">
                                            <i class="fas fa-arrow-right"></i> Explore
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Destination 6: Egypt -->
                    <div class="destination-slide">
                        <div class="destination-card">
                            <div class="destination-image">
                                <img src="{% static 'assets/images/place/place-5.jpg' %}" alt="Egypt" class="img-fluid">
                                <div class="destination-overlay">
                                    <div class="destination-content">
                                        <h4 class="destination-title">Egypt</h4>
                                        <p class="destination-description">Ancient pyramids and timeless Nile cruises</p>
                                        <div class="destination-price">Starting from $750</div>
                                        <a href="{% url 'users:all_packages' %}" class="destination-btn">
                                            <i class="fas fa-arrow-right"></i> Explore
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- View All Button -->
            <div class="row mt-5">
                <div class="col-12 text-center">
                    <a href="{% url 'users:all_packages' %}" class="view-all-destinations-btn wow fadeInUp" data-wow-delay="0.7s">
                        <i class="fas fa-globe-americas"></i> View All Destinations
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Destinations Section Styles -->
    <style>
        .featured-destinations-section {
            position: relative;
            overflow: hidden;
        }

        /* Slider Wrapper Styles */
        .destinations-slider-wrapper {
            position: relative;
            margin: 0 60px;
        }

        .destinations-slider {
            overflow: hidden;
        }

        /* Essential Slick Slider Overrides */
        .destinations-slider .slick-list {
            overflow: hidden;
            margin: 0 -15px;
        }

        .destinations-slider .slick-track {
            display: flex;
            align-items: stretch;
        }

        .destinations-slider .slick-slide {
            height: auto;
            padding: 0 15px;
            outline: none;
            box-sizing: border-box;
        }

        .destinations-slider .slick-slide > div {
            height: 100%;
        }

        .destination-slide {
            padding: 0;
            outline: none;
            height: 100%;
        }

        /* Show all slides before initialization for debugging */
        .destinations-slider:not(.slick-initialized) {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .destinations-slider:not(.slick-initialized) .destination-slide {
            flex: 0 0 calc(25% - 15px);
            display: block;
        }

        @media (max-width: 1200px) {
            .destinations-slider:not(.slick-initialized) .destination-slide {
                flex: 0 0 calc(33.333% - 15px);
            }
        }

        @media (max-width: 991px) {
            .destinations-slider:not(.slick-initialized) .destination-slide {
                flex: 0 0 calc(50% - 15px);
            }
        }

        @media (max-width: 767px) {
            .destinations-slider:not(.slick-initialized) .destination-slide {
                flex: 0 0 100%;
            }
        }

        /* Fix for potential Bootstrap conflicts */
        .destinations-slider .slick-slide img {
            display: block;
            width: 100%;
        }

        /* Ensure proper spacing */
        .destinations-slider .slick-list {
            padding: 0 !important;
        }

        /* Hide arrows when slider is not initialized */
        .destinations-slider-wrapper:not(.slick-initialized) .slick-prev,
        .destinations-slider-wrapper:not(.slick-initialized) .slick-next {
            display: none !important;
        }

        /* Force horizontal layout for Slick slider */
        .destinations-slider.slick-initialized {
            display: block !important;
        }

        .destinations-slider.slick-initialized .slick-track {
            display: flex !important;
            align-items: stretch !important;
        }

        .destinations-slider.slick-initialized .slick-slide {
            display: block !important;
            float: none !important;
            height: auto !important;
            min-height: 1px !important;
        }

        /* Ensure proper width calculation */
        .destinations-slider.slick-initialized .slick-slide > div {
            height: 100%;
            width: 100%;
        }

        /* Override any potential Bootstrap or other CSS conflicts */
        .destinations-slider .destination-slide {
            display: block !important;
            width: auto !important;
            float: none !important;
        }

        .destination-card {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            height: 450px !important;
            min-height: 450px;
            max-height: 450px;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 8px 25px rgba(23, 11, 44, 0.1);
            margin: 0 auto;
            max-width: 100%;
        }

        .destination-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(23, 11, 44, 0.2);
        }

        /* Navigation Arrows */
        .destinations-slider-wrapper .destinations-nav-prev,
        .destinations-slider-wrapper .destinations-nav-next,
        .destinations-slider-wrapper .slick-prev,
        .destinations-slider-wrapper .slick-next {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 55px;
            height: 55px;
            background: linear-gradient(135deg, #170b2c 0%, #0f238d 100%);
            border-radius: 50%;
            display: flex !important;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 100;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(23, 11, 44, 0.4);
            border: 3px solid rgba(255, 255, 255, 0.2);
            font-size: 0; /* Hide default text */
            line-height: 1;
            padding: 0;
            outline: none;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Focus styles for accessibility */
        .destinations-slider-wrapper .destinations-nav-prev:focus,
        .destinations-slider-wrapper .destinations-nav-next:focus,
        .destinations-slider-wrapper .slick-prev:focus,
        .destinations-slider-wrapper .slick-next:focus {
            outline: 3px solid #ff9d00;
            outline-offset: 2px;
        }

        .destinations-slider-wrapper .destinations-nav-prev,
        .destinations-slider-wrapper .slick-prev {
            left: -30px;
        }

        .destinations-slider-wrapper .destinations-nav-next,
        .destinations-slider-wrapper .slick-next {
            right: -30px;
        }

        .destinations-slider-wrapper .destinations-nav-prev:hover,
        .destinations-slider-wrapper .destinations-nav-next:hover,
        .destinations-slider-wrapper .slick-prev:hover,
        .destinations-slider-wrapper .slick-next:hover {
            background: #ffffff;
            border-color: #ff9d00;
            transform: translateY(-50%) scale(1.15);
            box-shadow: 0 8px 25px rgba(23, 11, 44, 0.5);
        }

        /* FontAwesome icons inside buttons */
        .destinations-slider-wrapper .destinations-nav-prev i,
        .destinations-slider-wrapper .destinations-nav-next i,
        .destinations-slider-wrapper .slick-prev i,
        .destinations-slider-wrapper .slick-next i {
            color: #ffffff !important;
            font-size: 1.4rem !important;
            transition: all 0.3s ease;
            opacity: 1 !important;
            display: inline-block !important;
            line-height: 1 !important;
            pointer-events: none;
        }

        /* Hide default Slick arrows content */
        .destinations-slider-wrapper .slick-prev:before,
        .destinations-slider-wrapper .slick-next:before {
            display: none !important;
            content: '' !important;
        }

        /* Hover effects for icons */
        .destinations-slider-wrapper .destinations-nav-prev:hover i,
        .destinations-slider-wrapper .destinations-nav-next:hover i,
        .destinations-slider-wrapper .slick-prev:hover i,
        .destinations-slider-wrapper .slick-next:hover i {
            color: #170b2c !important;
        }

        /* Ensure arrows are always visible */
        .destinations-slider-wrapper .slick-prev.slick-disabled,
        .destinations-slider-wrapper .slick-next.slick-disabled {
            opacity: 0.6 !important;
            display: flex !important;
        }

        .destinations-slider-wrapper .slick-prev.slick-disabled:hover,
        .destinations-slider-wrapper .slick-next.slick-disabled:hover {
            opacity: 0.8 !important;
        }

        /* Fallback CSS arrows if FontAwesome fails to load */
        .destinations-slider-wrapper .destinations-nav-prev:not(:has(i)),
        .destinations-slider-wrapper .slick-prev:not(:has(i)) {
            position: relative;
        }

        .destinations-slider-wrapper .destinations-nav-prev:not(:has(i)):after,
        .destinations-slider-wrapper .slick-prev:not(:has(i)):after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-30%, -50%) rotate(45deg);
            width: 8px;
            height: 8px;
            border-left: 2px solid #ffffff;
            border-bottom: 2px solid #ffffff;
        }

        .destinations-slider-wrapper .destinations-nav-next:not(:has(i)),
        .destinations-slider-wrapper .slick-next:not(:has(i)) {
            position: relative;
        }

        .destinations-slider-wrapper .destinations-nav-next:not(:has(i)):after,
        .destinations-slider-wrapper .slick-next:not(:has(i)):after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-70%, -50%) rotate(-45deg);
            width: 8px;
            height: 8px;
            border-right: 2px solid #ffffff;
            border-top: 2px solid #ffffff;
        }

        /* Alternative fallback using Unicode arrows */
        .destinations-slider-wrapper .destinations-nav-prev.no-fontawesome:before {
            content: '‹';
            font-size: 2rem;
            color: #ffffff;
            line-height: 1;
        }

        .destinations-slider-wrapper .destinations-nav-next.no-fontawesome:before {
            content: '›';
            font-size: 2rem;
            color: #ffffff;
            line-height: 1;
        }

        .destination-image {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .destination-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.6s ease;
        }

        .destination-card:hover .destination-image img {
            transform: scale(1.1);
        }

        .destination-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                to bottom,
                rgba(23, 11, 44, 0.1) 0%,
                rgba(23, 11, 44, 0.3) 50%,
                rgba(23, 11, 44, 0.9) 100%
            );
            display: flex;
            align-items: flex-end;
            padding: 30px;
            transition: all 0.4s ease;
        }

        .destination-card:hover .destination-overlay {
            background: linear-gradient(
                to bottom,
                rgba(15, 35, 141, 0.2) 0%,
                rgba(15, 35, 141, 0.5) 50%,
                rgba(15, 35, 141, 0.95) 100%
            );
        }

        .destination-content {
            color: white;
            width: 100%;
        }

        .destination-title {
            font-family: 'Fonarto', sans-serif;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: #ffffff;
        }

        .destination-description {
            font-size: 0.95rem;
            margin-bottom: 15px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.5;
        }

        .destination-price {
            font-size: 1.1rem;
            font-weight: 600;
            color: #ff9d00;
            margin-bottom: 20px;
        }

        .destination-btn {
            display: inline-flex;
            align-items: center;
            padding: 12px 25px;
            background: rgba(255, 157, 0, 0.9);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 2px solid transparent;
        }

        .destination-btn:hover {
            background: #ffffff;
            color: #170b2c;
            border-color: #ff9d00;
            transform: translateX(5px);
        }

        .destination-btn i {
            margin-right: 8px;
            transition: transform 0.3s ease;
        }

        .destination-btn:hover i {
            transform: translateX(3px);
        }

        .view-all-destinations-btn {
            display: inline-flex;
            align-items: center;
            padding: 18px 40px;
            background: linear-gradient(135deg, #170b2c 0%, #0f238d 100%);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.4s ease;
            box-shadow: 0 8px 25px rgba(23, 11, 44, 0.3);
            border: 3px solid transparent;
        }

        .view-all-destinations-btn:hover {
            background: #ffffff;
            color: #170b2c;
            border-color: #ff9d00;
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(23, 11, 44, 0.4);
        }

        .view-all-destinations-btn i {
            margin-right: 12px;
            font-size: 1.2rem;
            transition: transform 0.3s ease;
        }

        .view-all-destinations-btn:hover i {
            transform: rotate(360deg);
        }

        /* Responsive Design */
        @media (max-width: 991px) {
            .destinations-slider-wrapper {
                margin: 0 50px;
            }

            .destinations-slider-wrapper .destinations-nav-prev,
            .destinations-slider-wrapper .slick-prev {
                left: -25px;
            }

            .destinations-slider-wrapper .destinations-nav-next,
            .destinations-slider-wrapper .slick-next {
                right: -25px;
            }

            .destination-card {
                height: 410px !important;
                min-height: 410px;
                max-height: 410px;
            }

            .destination-title {
                font-size: 1.6rem;
            }

            .destination-overlay {
                padding: 25px;
            }
        }

        @media (max-width: 767px) {
            .destinations-slider-wrapper {
                margin: 0 40px;
            }

            .destinations-slider-wrapper .destinations-nav-prev,
            .destinations-slider-wrapper .destinations-nav-next,
            .destinations-slider-wrapper .slick-prev,
            .destinations-slider-wrapper .slick-next {
                width: 50px;
                height: 50px;
            }

            .destinations-slider-wrapper .destinations-nav-prev,
            .destinations-slider-wrapper .slick-prev {
                left: -20px;
            }

            .destinations-slider-wrapper .destinations-nav-next,
            .destinations-slider-wrapper .slick-next {
                right: -20px;
            }

            .destinations-slider-wrapper .destinations-nav-prev i,
            .destinations-slider-wrapper .destinations-nav-next i,
            .destinations-slider-wrapper .slick-prev i,
            .destinations-slider-wrapper .slick-next i {
                font-size: 1.2rem !important;
            }

            .destination-card {
                height: 370px !important;
                min-height: 370px;
                max-height: 370px;
            }

            .destination-title {
                font-size: 1.4rem;
            }

            .destination-overlay {
                padding: 20px;
            }

            .view-all-destinations-btn {
                padding: 15px 30px;
                font-size: 1rem;
            }

            .section-header h1 {
                font-size: 2.2rem;
            }
        }

        @media (max-width: 576px) {
            .destinations-slider-wrapper {
                margin: 0 30px;
            }

            .destinations-slider-wrapper .destinations-nav-prev,
            .destinations-slider-wrapper .destinations-nav-next,
            .destinations-slider-wrapper .slick-prev,
            .destinations-slider-wrapper .slick-next {
                width: 45px;
                height: 45px;
            }

            .destinations-slider-wrapper .destinations-nav-prev,
            .destinations-slider-wrapper .slick-prev {
                left: -15px;
            }

            .destinations-slider-wrapper .destinations-nav-next,
            .destinations-slider-wrapper .slick-next {
                right: -15px;
            }

            .destinations-slider-wrapper .destinations-nav-prev i,
            .destinations-slider-wrapper .destinations-nav-next i,
            .destinations-slider-wrapper .slick-prev i,
            .destinations-slider-wrapper .slick-next i {
                font-size: 1.1rem !important;
            }

            .destination-card {
                height: 320px !important;
                min-height: 320px;
                max-height: 320px;
            }

            .destination-overlay {
                padding: 15px;
            }

            .destination-btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }

            .section-header h1 {
                font-size: 1.8rem;
            }
        }
    </style>


    <!--====== End Featured Destinations Section ======-->

    <!-- Destinations Slider JavaScript -->
    <script>
        (function() {
            'use strict';

            // Function to initialize the destinations slider
            function initDestinationsSlider() {
                console.log('Attempting to initialize destinations slider...');

                // Check if jQuery is available
                if (typeof jQuery === 'undefined' && typeof $ === 'undefined') {
                    console.error('jQuery is not loaded');
                    return false;
                }

                // Use jQuery safely
                var $ = jQuery || window.$;

                // Check if Slick is available
                if (typeof $.fn.slick === 'undefined') {
                    console.error('Slick.js is not loaded');
                    return false;
                }

                var $slider = $('.destinations-slider');
                if (!$slider.length) {
                    console.error('Destinations slider element not found');
                    return false;
                }

                // Check if slider is already initialized
                if ($slider.hasClass('slick-initialized')) {
                    console.log('Slider already initialized');
                    return true;
                }

                console.log('Initializing destinations slider...');

                try {
                    // Initialize Destinations Slider with accessibility improvements
                    $slider.slick({
                        dots: false,
                        infinite: true,
                        speed: 600,
                        slidesToShow: 4,
                        slidesToScroll: 1,
                        autoplay: true,
                        autoplaySpeed: 4000,
                        pauseOnHover: true,
                        pauseOnFocus: true,
                        arrows: true,
                        accessibility: true,
                        focusOnSelect: false,
                        prevArrow: '<button type="button" class="destinations-nav-prev" aria-label="Previous destinations"><i class="fas fa-chevron-left" aria-hidden="true"></i></button>',
                        nextArrow: '<button type="button" class="destinations-nav-next" aria-label="Next destinations"><i class="fas fa-chevron-right" aria-hidden="true"></i></button>',
                        responsive: [
                            {
                                breakpoint: 1200,
                                settings: {
                                    slidesToShow: 3,
                                    slidesToScroll: 1
                                }
                            },
                            {
                                breakpoint: 991,
                                settings: {
                                    slidesToShow: 2,
                                    slidesToScroll: 1
                                }
                            },
                            {
                                breakpoint: 767,
                                settings: {
                                    slidesToShow: 1,
                                    slidesToScroll: 1,
                                    centerMode: true,
                                    centerPadding: '20px'
                                }
                            },
                            {
                                breakpoint: 576,
                                settings: {
                                    slidesToShow: 1,
                                    slidesToScroll: 1,
                                    centerMode: false,
                                    centerPadding: '0px'
                                }
                            }
                        ]
                    });

                    console.log('Destinations slider initialized successfully');

                    // Add hover effects and check FontAwesome after slider initialization
                    setTimeout(function() {
                        // Check if FontAwesome icons are loaded
                        checkFontAwesome();

                        $('.destination-card').hover(
                            function() {
                                if ($slider.hasClass('slick-initialized')) {
                                    $slider.slick('slickPause');
                                }
                            },
                            function() {
                                if ($slider.hasClass('slick-initialized')) {
                                    $slider.slick('slickPlay');
                                }
                            }
                        );
                    }, 500);

                    return true;

                } catch (error) {
                    console.error('Error initializing destinations slider:', error);
                    return false;
                }
            }

            // Function to check if FontAwesome is loaded and apply fallbacks
            function checkFontAwesome() {
                var $ = jQuery || window.$;

                // Check if FontAwesome CSS is loaded by testing a known class
                var testElement = $('<i class="fas fa-chevron-left" style="position: absolute; left: -9999px;"></i>').appendTo('body');
                var fontFamily = testElement.css('font-family');
                testElement.remove();

                // If FontAwesome is not loaded, apply fallback
                if (!fontFamily || fontFamily.indexOf('Font Awesome') === -1) {
                    console.warn('FontAwesome not detected, applying fallback arrows');

                    // Replace FontAwesome icons with Unicode arrows
                    $('.destinations-nav-prev, .slick-prev').each(function() {
                        var $this = $(this);
                        if ($this.find('i').length > 0) {
                            $this.find('i').replaceWith('<span style="font-size: 1.4rem; color: #ffffff;">‹</span>');
                        } else {
                            $this.addClass('no-fontawesome');
                        }
                    });

                    $('.destinations-nav-next, .slick-next').each(function() {
                        var $this = $(this);
                        if ($this.find('i').length > 0) {
                            $this.find('i').replaceWith('<span style="font-size: 1.4rem; color: #ffffff;">›</span>');
                        } else {
                            $this.addClass('no-fontawesome');
                        }
                    });
                } else {
                    console.log('FontAwesome loaded successfully');
                }
            }

            // Multiple initialization attempts with proper timing
            function attemptInitialization() {
                if (initDestinationsSlider()) {
                    return; // Success, stop trying
                }

                // Try again after a delay
                setTimeout(attemptInitialization, 1000);
            }

            // Initialize when DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', function() {
                    setTimeout(attemptInitialization, 100);
                });
            } else {
                setTimeout(attemptInitialization, 100);
            }

            // Fallback for window load
            window.addEventListener('load', function() {
                setTimeout(attemptInitialization, 200);
            });

        })();
    </script>

    <!-- Resource Loading Error Handling -->
    <script>
        (function() {
            'use strict';

            // Handle image loading errors
            document.addEventListener('DOMContentLoaded', function() {
                var images = document.querySelectorAll('img');
                images.forEach(function(img) {
                    img.addEventListener('error', function() {
                        console.warn('Failed to load image:', this.src);
                        // You can set a placeholder image here if needed
                        // this.src = '/static/assets/images/placeholder.jpg';
                    });
                });
            });

            // Check for critical script loading
            window.addEventListener('load', function() {
                var criticalScripts = {
                    'jQuery': typeof jQuery !== 'undefined',
                    'Slick.js': typeof jQuery !== 'undefined' && typeof jQuery.fn.slick !== 'undefined',
                    'WOW.js': typeof WOW !== 'undefined'
                };

                var missingScripts = [];
                for (var script in criticalScripts) {
                    if (!criticalScripts[script]) {
                        missingScripts.push(script);
                    }
                }

                if (missingScripts.length > 0) {
                    console.warn('Missing critical scripts:', missingScripts.join(', '));
                } else {
                    console.log('All critical scripts loaded successfully');
                }
            });
        })();
    </script>

            {% include 'users/footer.html' %}

       
{% endblock %}